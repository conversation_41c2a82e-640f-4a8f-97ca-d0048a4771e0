'use client'

import React from 'react'
import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { CalendarIcon, X, Check, type LucideIcon } from 'lucide-react'
import { TimePicker } from '@/components/ui/time-picker'
import { cn } from '../app/lib/utils'
import { format, isAfter, isBefore, set } from 'date-fns'
import type { DateRange } from 'react-day-picker'
import { Label, LabelPosition } from './ui/label'
import { Separator } from './ui/separator'
// Select components removed in favor of TimePicker

type DatePickerMode = 'range' | 'single'
type TimePickerMode = 'single' | 'range'
type DatePickerType = 'date' | 'datetime'

// Define icon type that can be either a React node or a Lucide icon component
type IconType = React.ReactNode | LucideIcon

interface DateValidation {
    minDate?: Date
    maxDate?: Date
    disabledDates?: Date[]
    disabledDaysOfWeek?: number[] // 0 = Sunday, 6 = Saturday
}

interface DatePickerProps
    extends Omit<
        React.ButtonHTMLAttributes<HTMLButtonElement>,
        'value' | 'onChange' | 'type'
    > {
    onChange: (value: any) => void
    className?: string
    placeholder?: string
    /**
     * Mode of the date picker.
     * 'range' for date range selection (default)
     * 'single' for single date selection
     */
    mode?: DatePickerMode
    /**
     * Type of the date picker.
     * 'date' for date only selection (default)
     * 'datetime' for date and time selection
     */
    type?: DatePickerType
    disabled?: boolean
    value?: Date | DateRange
    /**
     * Date format for display
     * @default 'LLL dd, y'
     */
    dateFormat?: string
    /** Validation rules for date selection */
    validation?: DateValidation
    /** Number of months to display in the calendar */
    numberOfMonths?: number
    /** Whether to close the popover when a date is selected (single mode only) */
    closeOnSelect?: boolean
    /** Whether to show week numbers */
    showWeekNumbers?: boolean
    allowFutureYears?: boolean

    showYearPicker?: boolean
    /** Whether to include time picker (deprecated, use type='datetime' instead) */
    includeTime?: boolean
    /** Mode of the time picker: 'single' or 'range' */
    timeMode?: TimePickerMode
    /** Time format for display */
    timeFormat?: string
    /** Interval between time options in minutes */
    timeInterval?: number
    /** Optional label for the date picker */
    label?: string
    /** Position of the label relative to the date picker */
    labelPosition?: LabelPosition
    /** Whether to show a clear button inside the date picker */
    clearable?: boolean
    /**
     * Custom icon to display in the date picker button.
     * Can be a Lucide icon component (CalendarIcon) or a JSX element (<CalendarIcon />)
     */
    icon?: IconType
    /** Whether to show a confirmation button after selecting a date/time */
    confirmSelection?: boolean
    /** Text to display on the confirmation button */
    confirmButtonText?: string
    /** Whether to use a modal for the date picker */
    modal?: boolean
}

const DatePicker = ({
    onChange,
    className,
    placeholder = 'Select date',
    mode = 'range',
    type = 'date',
    disabled = false,
    value,
    dateFormat = 'dd LLLL, y',
    validation,
    numberOfMonths = mode === 'range' ? 2 : 1,
    closeOnSelect = true,
    showWeekNumbers = false,
    showYearPicker = false,
    allowFutureYears = false,
    includeTime = false, // deprecated, use type='datetime' instead
    timeMode = 'single',
    timeFormat = 'HH:mm',
    timeInterval = 30,
    label,
    labelPosition = 'top',
    clearable = false,
    icon,
    confirmSelection = true,
    confirmButtonText = 'Confirm',
    modal = false,
    ...buttonProps
}: DatePickerProps) => {
    const [dateValue, setDateValue] = useState<DateRange | Date | undefined>(
        value ||
            (mode === 'range' ? { from: undefined, to: undefined } : undefined),
    )

    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component
    const [open, setOpen] = useState(false)
    const [time, setTime] = useState<{ hour: number; minute: number } | null>(
        null,
    )
    const [toTime, setToTime] = useState<{
        hour: number
        minute: number
    } | null>(null)

    // State to track pending selection when confirmation button is enabled
    const [pendingSelection, setPendingSelection] = useState<
        DateRange | Date | undefined
    >(value || undefined)

    // Set placeholder based on mode
    const actualPlaceholder =
        mode === 'range' ? 'Select date range' : 'Select date'

    useEffect(() => {
        // Deep equality check for dates to prevent unnecessary updates
        const isEqual = (a: any, b: any) => {
            if (a === b) return true

            // Handle Date objects
            if (a instanceof Date && b instanceof Date) {
                return a.getTime() === b.getTime()
            }

            // Handle DateRange objects
            if (a && b && typeof a === 'object' && typeof b === 'object') {
                if ('from' in a && 'to' in a && 'from' in b && 'to' in b) {
                    const fromEqual =
                        a.from && b.from
                            ? a.from instanceof Date &&
                              b.from instanceof Date &&
                              a.from.getTime() === b.from.getTime()
                            : a.from === b.from

                    const toEqual =
                        a.to && b.to
                            ? a.to instanceof Date &&
                              b.to instanceof Date &&
                              a.to.getTime() === b.to.getTime()
                            : a.to === b.to

                    return fromEqual && toEqual
                }
            }

            return false
        }

        // Only update if the value has actually changed
        if (!isEqual(value, dateValue)) {
            if (value) {
                setDateValue(value)
                // Also update pendingSelection with the value
                setPendingSelection(value)

                if (value instanceof Date) {
                    const hours = value.getHours()
                    const minutes = value.getMinutes()
                    if (hours || minutes)
                        setTime({ hour: hours, minute: minutes })
                } else if ('from' in value && value.from instanceof Date) {
                    const { from, to } = value
                    const fromHours = from.getHours()
                    const fromMinutes = from.getMinutes()
                    if (fromHours || fromMinutes)
                        setTime({ hour: fromHours, minute: fromMinutes })
                    if (to instanceof Date) {
                        const toHours = to.getHours()
                        const toMinutes = to.getMinutes()
                        if (toHours || toMinutes)
                            setToTime({ hour: toHours, minute: toMinutes })
                    }
                }
            } else {
                // When value is null/undefined, reset to initial state but maintain format
                setDateValue(
                    mode === 'range'
                        ? { from: undefined, to: undefined }
                        : undefined,
                )
                // Also reset pendingSelection
                setPendingSelection(undefined)
            }
        }
    }, [value, mode])

    const validateDate = (date: Date): boolean => {
        if (!validation) return true
        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } =
            validation
        if (minDate && isBefore(date, minDate)) return false
        if (maxDate && isAfter(date, maxDate)) return false
        if (
            disabledDates?.some(
                (disabledDate) =>
                    format(disabledDate, 'yyyy-MM-dd') ===
                    format(date, 'yyyy-MM-dd'),
            )
        )
            return false
        if (disabledDaysOfWeek?.includes(date.getDay())) return false
        return true
    }

    const applyTime = (
        date: Date | undefined,
        t: { hour: number; minute: number } | null,
    ) =>
        date && t
            ? set(date, {
                  hours: t.hour,
                  minutes: t.minute,
                  seconds: 0,
                  milliseconds: 0,
              })
            : date

    const handleValueChange = (newValue: DateRange | Date | undefined) => {
        if (!newValue) {
            // When a date is unselected, maintain the format consistency
            setDateValue(
                mode === 'range'
                    ? { from: undefined, to: undefined }
                    : undefined,
            )
            setPendingSelection(undefined)
            onChange(null)
            return
        }

        if (mode === 'range') {
            const { from, to } = newValue as DateRange
            if (from && !validateDate(from)) return
            if (to && !validateDate(to)) return

            // If confirmation is required, store the selection in pending state
            if (confirmSelection) {
                setPendingSelection({ from, to })
            } else {
                setDateValue({ from, to })

                // If time is not set, initialize it with current time
                let currentTime = time
                if (!currentTime && shouldIncludeTime) {
                    const now = new Date()
                    currentTime = {
                        hour: now.getHours(),
                        minute: now.getMinutes(),
                    }
                    setTime(currentTime)
                }

                let currentToTime = toTime
                if (!currentToTime && shouldIncludeTime && to) {
                    const now = new Date()
                    currentToTime = {
                        hour: now.getHours(),
                        minute: now.getMinutes(),
                    }
                    setToTime(currentToTime)
                }

                onChange({
                    startDate: applyTime(from, currentTime),
                    endDate: applyTime(to, currentToTime),
                })
            }
        } else {
            const singleDate = newValue as Date
            if (!validateDate(singleDate)) return

            // If confirmation is required, store the selection in pending state
            if (confirmSelection) {
                setPendingSelection(singleDate)
            } else {
                setDateValue(singleDate)

                // If time is not set and we should include time, preserve existing time or initialize
                let currentTime = time

                // If we have an existing dateValue with time, preserve that time
                if (
                    !currentTime &&
                    shouldIncludeTime &&
                    dateValue instanceof Date
                ) {
                    currentTime = {
                        hour: dateValue.getHours(),
                        minute: dateValue.getMinutes(),
                    }
                    setTime(currentTime)
                } else if (!currentTime && shouldIncludeTime) {
                    // Only use current time if no existing time is available
                    const now = new Date()
                    currentTime = {
                        hour: now.getHours(),
                        minute: now.getMinutes(),
                    }
                    setTime(currentTime)
                }

                // Always apply time if shouldIncludeTime is true
                const result = shouldIncludeTime
                    ? applyTime(singleDate, currentTime)
                    : singleDate

                onChange(result)

                if (closeOnSelect && !shouldIncludeTime) setOpen(false)
            }
        }
    }

    const handleTimeChange = (date: Date, isToTime = false) => {
        if (!date) return

        const newTime = { hour: date.getHours(), minute: date.getMinutes() }

        // Check if the time has actually changed before updating state
        if (isToTime) {
            const currentToTime = toTime

            if (
                !currentToTime ||
                currentToTime.hour !== newTime.hour ||
                currentToTime.minute !== newTime.minute
            ) {
                setToTime(newTime)

                // Update pendingSelection with the new time
                if (confirmSelection && (pendingSelection as DateRange)?.to) {
                    const newEndDate = set(
                        (pendingSelection as DateRange).to!,
                        {
                            hours: newTime.hour,
                            minutes: newTime.minute,
                            seconds: 0,
                            milliseconds: 0,
                        },
                    )

                    setPendingSelection({
                        ...(pendingSelection as DateRange),
                        to: newEndDate,
                    })
                }

                // If confirmation is not required, call onChange directly
                if (!confirmSelection) {
                    if ((dateValue as DateRange)?.to) {
                        const newEndDate = set((dateValue as DateRange).to!, {
                            hours: newTime.hour,
                            minutes: newTime.minute,
                            seconds: 0,
                            milliseconds: 0,
                        })

                        onChange({
                            startDate: (dateValue as DateRange).from,
                            endDate: newEndDate,
                        })
                    }
                }
            }
        } else {
            const currentTime = time

            if (
                !currentTime ||
                currentTime.hour !== newTime.hour ||
                currentTime.minute !== newTime.minute
            ) {
                setTime(newTime)

                // Update pendingSelection with the new time
                if (confirmSelection) {
                    if ((pendingSelection as DateRange)?.from) {
                        const newStartDate = set(
                            (pendingSelection as DateRange).from!,
                            {
                                hours: newTime.hour,
                                minutes: newTime.minute,
                                seconds: 0,
                                milliseconds: 0,
                            },
                        )

                        setPendingSelection({
                            ...(pendingSelection as DateRange),
                            from: newStartDate,
                        })
                    } else if (pendingSelection instanceof Date) {
                        const newDate = set(pendingSelection as Date, {
                            hours: newTime.hour,
                            minutes: newTime.minute,
                            seconds: 0,
                            milliseconds: 0,
                        })

                        setPendingSelection(newDate)
                    }
                }

                // If confirmation is not required, call onChange directly
                if (!confirmSelection) {
                    if ((dateValue as DateRange)?.from) {
                        const newStartDate = set(
                            (dateValue as DateRange).from!,
                            {
                                hours: newTime.hour,
                                minutes: newTime.minute,
                                seconds: 0,
                                milliseconds: 0,
                            },
                        )

                        onChange({
                            startDate: newStartDate,
                            endDate: (dateValue as DateRange).to,
                        })
                    } else if (dateValue instanceof Date) {
                        const newDate = set(dateValue, {
                            hours: newTime.hour,
                            minutes: newTime.minute,
                            seconds: 0,
                            milliseconds: 0,
                        })

                        onChange(newDate)
                    }
                }
            }
        }
    }

    const handleClear = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation() // Prevent triggering the popover
        setDateValue(
            mode === 'range' ? { from: undefined, to: undefined } : undefined,
        )
        setPendingSelection(undefined)
        setTime(null)
        setToTime(null)
        onChange(null)
    }

    // Function to handle clear button click inside the popover
    const handleClearInPopover = () => {
        setDateValue(
            mode === 'range' ? { from: undefined, to: undefined } : undefined,
        )
        setPendingSelection(undefined)
        setTime(null)
        setToTime(null)
        onChange(null)
        // Close the popover after clearing
        setOpen(false)
    }

    // Function to handle confirmation button click
    const handleConfirm = () => {
        if (!pendingSelection) return

        if (mode === 'range') {
            const { from, to } = pendingSelection as DateRange
            setDateValue({ from, to })

            // Apply time if needed
            let currentTime = time
            if (!currentTime && shouldIncludeTime && from) {
                const now = new Date()
                currentTime = { hour: now.getHours(), minute: now.getMinutes() }
                setTime(currentTime)
            }

            let currentToTime = toTime
            if (!currentToTime && shouldIncludeTime && to) {
                const now = new Date()
                currentToTime = {
                    hour: now.getHours(),
                    minute: now.getMinutes(),
                }
                setToTime(currentToTime)
            }

            onChange({
                startDate: applyTime(from, currentTime),
                endDate: applyTime(to, currentToTime),
            })
        } else {
            const singleDate = pendingSelection as Date
            setDateValue(singleDate)

            // Apply time if needed
            let currentTime = time

            // If we have an existing dateValue with time, preserve that time
            if (
                !currentTime &&
                shouldIncludeTime &&
                dateValue instanceof Date
            ) {
                currentTime = {
                    hour: dateValue.getHours(),
                    minute: dateValue.getMinutes(),
                }
                setTime(currentTime)
            } else if (!currentTime && shouldIncludeTime) {
                // Only use current time if no existing time is available
                const now = new Date()
                currentTime = { hour: now.getHours(), minute: now.getMinutes() }
                setTime(currentTime)
            }

            const result = shouldIncludeTime
                ? applyTime(singleDate, currentTime)
                : singleDate

            onChange(result)
        }

        // Close the popover after confirmation
        setOpen(false)
        // Reset pending selection
        setPendingSelection(undefined)
    }

    // timeOptions removed as we're now using TimePicker component

    // Determine if we should include time based on the type prop or the deprecated includeTime prop
    const [shouldIncludeTime, setShouldIncludeTime] = useState(
        type === 'datetime' || includeTime,
    )

    // Update shouldIncludeTime when type or includeTime changes
    useEffect(() => {
        setShouldIncludeTime(type === 'datetime' || includeTime)
    }, [type, includeTime])

    const displayTimeFormat = shouldIncludeTime
        ? `${dateFormat} ${timeFormat}`
        : dateFormat

    // Guard against invalid dates
    const formatDateWithTime = (date: Date | undefined) => {
        if (!date) return ''
        const validDate = date instanceof Date ? date : new Date()
        return isNaN(validDate.getTime())
            ? ''
            : format(validDate, displayTimeFormat)
    }

    // Time picker implementation now uses the TimePicker component with mode option

    if (disabled) {
        // Use the provided value if available, otherwise use current date
        const displayDate = (date: Date | undefined) => {
            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
                return format(new Date(), dateFormat)
            }
            return format(
                date,
                shouldIncludeTime ? displayTimeFormat : dateFormat,
            )
        }

        // Format the date based on the mode and value
        let displayValue
        if (mode === 'range') {
            const range = dateValue as DateRange
            if (range?.from) {
                displayValue = range.to
                    ? `${displayDate(range.from)} - ${displayDate(range.to)}`
                    : displayDate(range.from)
            } else {
                const currentFormatted = format(new Date(), dateFormat)
                displayValue = `${currentFormatted} - ${currentFormatted}`
            }
        } else {
            displayValue =
                dateValue instanceof Date
                    ? displayDate(dateValue as Date)
                    : format(new Date(), dateFormat)
        }

        return (
            <div>
                {label && (
                    <Label
                        asChild
                        id="date"
                        position={labelPosition}
                        disabled={disabled}>
                        {label}
                    </Label>
                )}
                <div className="relative w-full">
                    <Button
                        id="date"
                        variant="outline"
                        disabled={disabled}
                        className={cn('px-4 justify-start w-full')}
                        iconLeft={
                            icon ? (
                                React.isValidElement(icon) ? (
                                    React.cloneElement(
                                        icon as React.ReactElement,
                                        {
                                            className:
                                                'mr-[8.5px] w-5 h-5 text-neutral-400',
                                        },
                                    )
                                ) : (
                                    React.createElement(icon as any, {
                                        size: 20,
                                        className:
                                            'mr-[8.5px] w-5 h-5 text-neutral-400',
                                    })
                                )
                            ) : (
                                <CalendarIcon className="mr-[8.5px] w-5 h-5 text-neutral-400" />
                            )
                        }
                        {...buttonProps}
                        type="button">
                        {displayValue}
                    </Button>
                </div>
            </div>
        )
    }

    const renderButtonLabel = () => {
        if (mode === 'range') {
            const range = dateValue as DateRange
            if (range?.from) {
                return range.to ? (
                    <>
                        {formatDateWithTime(range.from)} -{' '}
                        {formatDateWithTime(range.to)}
                    </>
                ) : (
                    formatDateWithTime(range.from)
                )
            }
            // Use consistent date format for placeholder
            return (
                <span className="text-muted-foreground">
                    {actualPlaceholder}
                </span>
            )
        } else if (dateValue) {
            return formatDateWithTime(dateValue as Date)
        }
        // Use consistent date format for placeholder
        return (
            <span className="text-muted-foreground">{actualPlaceholder}</span>
        )
    }

    return (
        <div>
            {label && (
                <Label id="date" position={labelPosition} disabled={disabled}>
                    {label}
                </Label>
            )}
            <Popover
                modal={modal}
                open={open}
                onOpenChange={(isOpen) => {
                    // When opening the popover, initialize pendingSelection with the current value
                    if (isOpen && !pendingSelection && dateValue) {
                        setPendingSelection(dateValue)
                    }
                    setOpen(isOpen)
                }}>
                <PopoverTrigger asChild>
                    <div className="relative w-full">
                        <Button
                            id="date"
                            variant="outline"
                            disabled={disabled}
                            iconLeft={
                                icon ? (
                                    React.isValidElement(icon) ? (
                                        icon
                                    ) : (
                                        React.createElement(icon as any, {
                                            size: 20,
                                            className: 'text-muted-foreground',
                                        })
                                    )
                                ) : (
                                    <CalendarIcon
                                        size={20}
                                        className="text-neutral-400"
                                    />
                                )
                            }
                            className={cn(
                                'px-4 justify-start w-full',
                                clearable && 'pr-10',
                            )}
                            {...buttonProps}
                            type="button">
                            {renderButtonLabel()}
                        </Button>
                        {clearable &&
                            (dateValue instanceof Date ||
                                (dateValue as DateRange)?.from) && (
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                                    onClick={handleClear}
                                    aria-label="Clear date">
                                    <X
                                        size={16}
                                        className="text-neutral-400 hover:text-background0"
                                    />
                                </Button>
                            )}
                    </div>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                    {mode === 'range' ? (
                        <div>
                            <Calendar
                                key="range"
                                initialFocus
                                mode="range"
                                month={
                                    (confirmSelection && pendingSelection
                                        ? (pendingSelection as DateRange)?.from
                                        : (dateValue as DateRange)?.from) ||
                                    new Date()
                                }
                                allowFutureYears={allowFutureYears}
                                showYearPicker={showYearPicker}
                                selected={
                                    confirmSelection && pendingSelection
                                        ? (pendingSelection as DateRange)
                                        : (dateValue as DateRange)
                                }
                                onSelect={(value) => {
                                    // Ensure we maintain the date format when a date is unselected
                                    handleValueChange(value)
                                }}
                                numberOfMonths={numberOfMonths}
                                showWeekNumber={showWeekNumbers}
                                disabled={
                                    validation
                                        ? (date) => !validateDate(date)
                                        : undefined
                                }
                            />
                            {shouldIncludeTime && (
                                <div className="p-3 border-t border-border">
                                    <div className="mt-1">
                                        <TimePicker
                                            value={(() => {
                                                // When confirmation is enabled, use time from pendingSelection if available
                                                let currentTime = time
                                                if (
                                                    confirmSelection &&
                                                    (
                                                        pendingSelection as DateRange
                                                    )?.from
                                                ) {
                                                    const fromDate = (
                                                        pendingSelection as DateRange
                                                    ).from!
                                                    currentTime = {
                                                        hour: fromDate.getHours(),
                                                        minute: fromDate.getMinutes(),
                                                    }
                                                }

                                                if (currentTime) {
                                                    const date = new Date()
                                                    date.setHours(
                                                        currentTime.hour,
                                                        currentTime.minute,
                                                        0,
                                                        0,
                                                    )
                                                    return date
                                                }
                                                return undefined
                                            })()}
                                            toValue={(() => {
                                                // When confirmation is enabled, use time from pendingSelection if available
                                                let currentToTime = toTime
                                                if (
                                                    confirmSelection &&
                                                    (
                                                        pendingSelection as DateRange
                                                    )?.to
                                                ) {
                                                    const toDate = (
                                                        pendingSelection as DateRange
                                                    ).to!
                                                    currentToTime = {
                                                        hour: toDate.getHours(),
                                                        minute: toDate.getMinutes(),
                                                    }
                                                }

                                                if (currentToTime) {
                                                    const date = new Date()
                                                    date.setHours(
                                                        currentToTime.hour,
                                                        currentToTime.minute,
                                                        0,
                                                        0,
                                                    )
                                                    return date
                                                }
                                                return undefined
                                            })()}
                                            onChange={handleTimeChange}
                                            onToChange={(date) =>
                                                handleTimeChange(date, true)
                                            }
                                            // Always enable time picker regardless of whether a date is selected
                                            disabled={false}
                                            className="w-full"
                                            mode={timeMode}
                                            label={
                                                timeMode === 'range'
                                                    ? 'Time Range'
                                                    : undefined
                                            }
                                        />
                                    </div>
                                </div>
                            )}
                            {confirmSelection && (
                                <>
                                    <Separator className="my-0" />
                                    <div
                                        className={cn(
                                            'p-3 flex gap-3 justify-end',
                                        )}>
                                        {clearable && (
                                            <div className="w-fit">
                                                <Button
                                                    variant="outline"
                                                    onClick={
                                                        handleClearInPopover
                                                    }
                                                    iconLeft={X}
                                                    className="w-fit"
                                                    aria-label="Clear date range">
                                                    Clear
                                                </Button>
                                            </div>
                                        )}
                                        <Button
                                            onClick={handleConfirm}
                                            disabled={!pendingSelection}
                                            iconLeft={Check}>
                                            {confirmButtonText}
                                        </Button>
                                    </div>
                                </>
                            )}
                        </div>
                    ) : (
                        <div>
                            <Calendar
                                key="single"
                                initialFocus
                                mode="single"
                                month={
                                    (confirmSelection && pendingSelection
                                        ? (pendingSelection as Date)
                                        : (dateValue as Date)) || new Date()
                                }
                                showYearPicker={showYearPicker}
                                allowFutureYears={allowFutureYears}
                                selected={
                                    confirmSelection && pendingSelection
                                        ? (pendingSelection as Date)
                                        : (dateValue as Date)
                                }
                                onSelect={(value) => {
                                    // Ensure we maintain the date format when a date is unselected
                                    handleValueChange(value)
                                }}
                                numberOfMonths={numberOfMonths}
                                showWeekNumber={showWeekNumbers}
                                disabled={
                                    validation
                                        ? (date) => !validateDate(date)
                                        : undefined
                                }
                            />
                            {shouldIncludeTime && (
                                <div className="p-3 border-t border-border">
                                    <div className="mt-1">
                                        <TimePicker
                                            value={(() => {
                                                // When confirmation is enabled, use time from pendingSelection if available
                                                let currentTime = time

                                                if (
                                                    confirmSelection &&
                                                    pendingSelection instanceof
                                                        Date &&
                                                    !isNaN(
                                                        pendingSelection.getTime(),
                                                    )
                                                ) {
                                                    currentTime = {
                                                        hour: pendingSelection.getHours(),
                                                        minute: pendingSelection.getMinutes(),
                                                    }
                                                }

                                                if (currentTime) {
                                                    const date = new Date()
                                                    date.setHours(
                                                        currentTime.hour,
                                                        currentTime.minute,
                                                        0,
                                                        0,
                                                    )
                                                    return date
                                                }
                                                return undefined
                                            })()}
                                            toValue={
                                                toTime
                                                    ? (() => {
                                                          const date =
                                                              new Date()
                                                          date.setHours(
                                                              toTime.hour,
                                                              toTime.minute,
                                                              0,
                                                              0,
                                                          )
                                                          return date
                                                      })()
                                                    : undefined
                                            }
                                            onChange={handleTimeChange}
                                            onToChange={(date) =>
                                                handleTimeChange(date, true)
                                            }
                                            // Always enable time picker regardless of whether a date is selected
                                            disabled={false}
                                            className="w-full"
                                            mode={timeMode}
                                            label={
                                                timeMode === 'range'
                                                    ? 'Time Range'
                                                    : undefined
                                            }
                                        />
                                    </div>
                                </div>
                            )}
                            {confirmSelection && (
                                <>
                                    <Separator className="my-0" />
                                    <div
                                        className={cn(
                                            'p-3 flex gap-3 justify-end',
                                        )}>
                                        {clearable && (
                                            <div className="w-fit">
                                                <Button
                                                    variant="outline"
                                                    onClick={
                                                        handleClearInPopover
                                                    }
                                                    iconLeft={X}
                                                    aria-label="Clear date">
                                                    Clear
                                                </Button>
                                            </div>
                                        )}
                                        <Button
                                            onClick={handleConfirm}
                                            disabled={!pendingSelection}
                                            className={cn(
                                                clearable ? '' : 'w-full',
                                            )}>
                                            {confirmButtonText}
                                        </Button>
                                    </div>
                                </>
                            )}
                        </div>
                    )}
                </PopoverContent>
            </Popover>
        </div>
    )
}

export default DatePicker
