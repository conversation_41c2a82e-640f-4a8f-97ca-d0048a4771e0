'use client'

import { Dispatch, useState } from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ields from './engineer-mechanical-fields'
import <PERSON><PERSON><PERSON><PERSON>Fields from './engineer-generator-fields'
import EngineerElectronicsFields from './engineer-electronics-fields'
import EngineerTowlineWinchFields from './engineer-towline-winch-fields'
import <PERSON><PERSON><PERSON><PERSON> from '../../crew-checker'
import dayjs from 'dayjs'
import { Card } from '@/components/ui'
import { Separator } from '@/components/ui/separator'

interface IProps {
    logentryID: any
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    offline?: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    updateVesselDailyCheck_LogBookEntrySection: Function
    fieldImages: any
    refreshImages: any
}

export function EngineeringCheckFields({
    logentryID,
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    offline = false,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    updateVesselDailyCheck_LogBookEntrySection,
    fieldImages,
    refreshImages,
}: IProps) {
    const [engrCrewResponsible, setEngrCrewResponsible] = useState<any>(
        vesselDailyCheck?.engrCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )
    const [engrCheckTime, setEngrCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.engrCheckTime ?? new Date()),
    )

    const handleEngrCheckTime = async (date: any) => {
        setEngrCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                engrCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log(' offline')
                /* const newVesselDailyCheck =
                        await dailyCheckModel.save(variables)
                    // setSaving(true)
                    setSaving(false)
                    setVesselDailyCheck([newVesselDailyCheck])
                    const sections = logbook.logBookEntrySections.nodes
                    const section = {
                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                        id: `${vesselDailyCheck.id}`,
                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                    }
                    if (
                        !sections.some(
                            (s: any) =>
                                JSON.stringify(s) === JSON.stringify(section),
                        )
                    ) {
                        sections.push(section)
                    }
                    const lb = {
                        ...logbook,
                        logBookEntrySections: { nodes: sections },
                    }
                    await logBookModel.save(lb)
                    getOfflineLogBookEntry() */
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleEngrCrewResponsible = async (crews: any) => {
        setEngrCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                engrCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log(' offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    return (
        <Card>
            <EngineerMechanicalFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                fieldImages={fieldImages}
                refreshImages={refreshImages}
            />
            <EngineerGeneratorFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                fieldImages={fieldImages}
                refreshImages={refreshImages}
            />
            <EngineerElectronicsFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                fieldImages={fieldImages}
                refreshImages={refreshImages}
            />
            <EngineerTowlineWinchFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                fieldImages={fieldImages}
                refreshImages={refreshImages}
            />

            <CrewChecker
                crewKey="EngrCrewResponsible"
                timeKey="EngrCheckTime"
                logBookConfig={logBookConfig}
                locked={locked}
                offline={offline}
                edit_logBookEntry={edit_logBookEntry}
                setCrewResponsible={handleEngrCrewResponsible}
                crewResponsible={engrCrewResponsible}
                checkTime={engrCheckTime}
                handleCheckTime={handleEngrCheckTime}
                setCheckTime={setEngrCheckTime}
            />
        </Card>
    )
}
