"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, showYearPicker = false, allowFutureYears = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, smartViewport = false, minSpaceBelow = 400, ...buttonProps } = param;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Deep equality check for dates to prevent unnecessary updates\n        const isEqual = (a, b)=>{\n            if (a === b) return true;\n            // Handle Date objects\n            if (a instanceof Date && b instanceof Date) {\n                return a.getTime() === b.getTime();\n            }\n            // Handle DateRange objects\n            if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n                if (\"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n                    const fromEqual = a.from && b.from ? a.from instanceof Date && b.from instanceof Date && a.from.getTime() === b.from.getTime() : a.from === b.from;\n                    const toEqual = a.to && b.to ? a.to instanceof Date && b.to instanceof Date && a.to.getTime() === b.to.getTime() : a.to === b.to;\n                    return fromEqual && toEqual;\n                }\n            }\n            return false;\n        };\n        // Only update if the value has actually changed\n        if (!isEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const hours = value.getHours();\n                    const minutes = value.getMinutes();\n                    if (hours || minutes) setTime({\n                        hour: hours,\n                        minute: minutes\n                    });\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromHours = from.getHours();\n                    const fromMinutes = from.getMinutes();\n                    if (fromHours || fromMinutes) setTime({\n                        hour: fromHours,\n                        minute: fromMinutes\n                    });\n                    if (to instanceof Date) {\n                        const toHours = to.getHours();\n                        const toMinutes = to.getMinutes();\n                        if (toHours || toMinutes) setToTime({\n                            hour: toHours,\n                            minute: toMinutes\n                        });\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(mode === \"range\" ? {\n                    from: undefined,\n                    to: undefined\n                } : undefined);\n                // Also reset pendingSelection\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(mode === \"range\" ? {\n                from: undefined,\n                to: undefined\n            } : undefined);\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // If time is not set, initialize it with current time\n                let currentTime = time;\n                if (!currentTime && shouldIncludeTime) {\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                let currentToTime = toTime;\n                if (!currentToTime && shouldIncludeTime && to) {\n                    const now = new Date();\n                    currentToTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setToTime(currentToTime);\n                }\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // If time is not set and we should include time, preserve existing time or initialize\n                let currentTime = time;\n                // If we have an existing dateValue with time, preserve that time\n                if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                    currentTime = {\n                        hour: dateValue.getHours(),\n                        minute: dateValue.getMinutes()\n                    };\n                    setTime(currentTime);\n                } else if (!currentTime && shouldIncludeTime) {\n                    // Only use current time if no existing time is available\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n        // Close the popover after clearing\n        setOpen(false);\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Apply time if needed\n            let currentTime = time;\n            if (!currentTime && shouldIncludeTime && from) {\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            let currentToTime = toTime;\n            if (!currentToTime && shouldIncludeTime && to) {\n                const now = new Date();\n                currentToTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setToTime(currentToTime);\n            }\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Apply time if needed\n            let currentTime = time;\n            // If we have an existing dateValue with time, preserve that time\n            if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                currentTime = {\n                    hour: dateValue.getHours(),\n                    minute: dateValue.getMinutes()\n                };\n                setTime(currentTime);\n            } else if (!currentTime && shouldIncludeTime) {\n                // Only use current time if no existing time is available\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 640,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                            size: 20,\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 33\n                        }, void 0),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 648,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 638,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 699,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 708,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 715,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? icon : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                                        size: 20,\n                                        className: \"text-muted-foreground\"\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20,\n                                        className: \"text-neutral-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 37\n                                    }, void 0),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    initialFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    allowFutureYears: allowFutureYears,\n                                    showYearPicker: showYearPicker,\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from)) {\n                                                    const fromDate = pendingSelection.from;\n                                                    currentTime = {\n                                                        hour: fromDate.getHours(),\n                                                        minute: fromDate.getMinutes()\n                                                    };\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            toValue: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentToTime = toTime;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                                                    const toDate = pendingSelection.to;\n                                                    currentToTime = {\n                                                        hour: toDate.getHours(),\n                                                        minute: toDate.getMinutes()\n                                                    };\n                                                }\n                                                if (currentToTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentToTime.hour, currentToTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 894,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 780,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    initialFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    showYearPicker: showYearPicker,\n                                    allowFutureYears: allowFutureYears,\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && pendingSelection instanceof Date && !isNaN(pendingSelection.getTime())) {\n                                                    currentTime = {\n                                                        hour: pendingSelection.getHours(),\n                                                        minute: pendingSelection.getMinutes()\n                                                    };\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            toValue: toTime ? (()=>{\n                                                const date = new Date();\n                                                date.setHours(toTime.hour, toTime.minute, 0, 0);\n                                                return date;\n                                            })() : undefined,\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 954,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1020,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1038,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 778,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 719,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 713,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});