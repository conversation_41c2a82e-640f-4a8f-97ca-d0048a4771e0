'use client'
import React, { useEffect, useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'

import {
    DailyCheckField,
    CheckField,
    CheckFieldTopContent,
    CheckFieldContent,
} from '@/components/daily-check-field'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import {
    GET_SECTION_MEMBER_COMMENTS,
    GET_SECTION_MEMBER_IMAGES,
    VesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import { useSearchParams } from 'next/navigation'

import {
    displayDescription,
    displayField,
    getFilteredFields,
    getSortOrder,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'

// Sheet components removed as they're no longer needed
import 'react-quill/dist/quill.snow.css'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import CrewChecker from './crew-checker'
import dayjs from 'dayjs'
import { ActionFooter } from '@/components/ui/action-footer'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'
import { Card } from '@/components/ui'
import { useDebounceFn } from '@reactuses/core'

export default function JetSpecific({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    setTab, // Kept for backward compatibility
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab?: any // Made optional
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages: any
    refreshImages: any
}) {
    // Router removed as it's no longer needed with ActionFooter
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const [saving, setSaving] = useState(false)
    const tab = searchParams.get('tab') ?? ''
    const [sectionComment, setSectionComment] = useState<any>('')
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState<
        string | React.ReactNode
    >('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()

    // Note: The following state variables and their handlers are used indirectly
    // through the fields array defined below. They are referenced by the field handlers.

    const [jetCrewResponsible, setJetCrewResponsible] = useState<any>(
        vesselDailyCheck?.jetCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )
    const [jetCheckTime, setJetCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.jetCheckTime ?? new Date()),
    )

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [])

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [tab])

    const handleJetSpecificChecks = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const variable = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await dailyCheckModel.save(variable)
                setVesselDailyCheck([data])
                // setSaving(true)
                setSaving(false)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variable,
                    },
                })
            }
        }
    }

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing safety check', error)
                console.error(
                    'Error details:',
                    error.message,
                    error.graphQLErrors,
                )
            },
        },
    )

    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    // Helper function to handle saving field comments
    const handleSaveFieldComment = async (
        fieldName: string,
        commentText: string,
    ) => {
        const comment = getComment(fieldName)
        let id = comment?.id ? comment.id : 0
        if (offline) {
            id = comment?.id ? comment.id : generateUniqueId()
        }
        const variables = {
            id: id,
            fieldName: fieldName,
            comment: commentText,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        if (comment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const fields = [
        {
            name: 'ForwardAndReverseBelts',
            label: 'Forward & reverse belts',
            value: 'forwardAndReverseBelts',
            checked: vesselDailyCheck?.forwardAndReverseBelts,
            sortOrder: getSortOrder('ForwardAndReverseBelts', logBookConfig),
        },
        {
            name: 'SteeringTiller',
            label: 'Steering, tiller, arm bolts',
            value: 'steeringTiller',
            checked: vesselDailyCheck?.steeringTiller,
            sortOrder: getSortOrder('SteeringTiller', logBookConfig),
        },
        {
            name: 'UnitTransomBolts',
            label: 'Unit transom bolts',
            value: 'unitTransomBolts',
            checked: vesselDailyCheck?.unitTransomBolts,
            sortOrder: getSortOrder('UnitTransomBolts', logBookConfig),
        },
        {
            name: 'CotterPins',
            label: 'Cotter pins',
            value: 'cotterPins',
            checked: vesselDailyCheck?.cotterPins,
            sortOrder: getSortOrder('CotterPins', logBookConfig),
        },
        {
            name: 'ReverseBucketAndRam',
            label: 'Reverse bucket & rams',
            value: 'reverseBucketAndRam',
            checked: vesselDailyCheck?.reverseBucketAndRam,
            sortOrder: getSortOrder('ReverseBucketAndRam', logBookConfig),
        },
        {
            name: 'NozzleAndBearings',
            label: 'Nozzle & bearings',
            value: 'nozzleAndBearings',
            checked: vesselDailyCheck?.nozzleAndBearings,
            sortOrder: getSortOrder('NozzleAndBearings', logBookConfig),
        },
        {
            name: 'TailHousing',
            label: 'Tail housing & bolts',
            value: 'tailHousing',
            checked: vesselDailyCheck?.tailHousing,
            sortOrder: getSortOrder('TailHousing', logBookConfig),
        },
    ]

    const handleSave = async () => {
        if (offline) {
            const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
            setVesselDailyCheck(data)
            setSaving(true)
        } else {
            getSectionVesselDailyCheck_LogBookEntrySection({
                variables: {
                    id: [vesselDailyCheck.id],
                },
            })
        }
        // Handle section comment
        let id = 0
        if (offline) {
            const comments = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            const sectionComment = comments.filter(
                (comment: any) =>
                    comment.fieldName === 'JetSpecific' &&
                    comment.commentType === 'Section',
            )
            if (sectionComment.length > 0) {
                id = sectionComment[0].id
            } else {
                id = generateUniqueId()
            }
        }
        const variables = {
            id: id,
            fieldName: 'JetSpecific',
            comment: sectionComment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        if (id > 0 && offline) {
            await commentModel.save(variables)
            loadSectionMemberComments()
        } else if (!offline && id > 0) {
            updateSectionMemberComment({
                variables: { input: variables },
            })
        } else if (sectionComment) {
            if (offline) {
                const offlineID = getComment('JetSpecific', 'Section')
                    ? getComment('JetSpecific', 'Section').id
                    : generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const handleJetCrewResponsible = async (crews: any) => {
        setJetCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                jetCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log(' offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleJetCheckTime = async (date: any) => {
        setJetCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                jetCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log(' offline')
                {
                    /*
                    Commented out offline functionality code:
                    const newVesselDailyCheck = await dailyCheckModel.save(variables)
                    // setSaving(true)
                    setSaving(false)
                    setVesselDailyCheck([newVesselDailyCheck])
                    const sections = logbook.logBookEntrySections.nodes
                    const section = {
                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                        id: `${vesselDailyCheck.id}`,
                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                    }
                    if (
                        !sections.some(
                            (s: any) =>
                                JSON.stringify(s) === JSON.stringify(section),
                        )
                    ) {
                        sections.push(section)
                    }
                    const lb = {
                        ...logbook,
                        logBookEntrySections: { nodes: sections },
                    }
                    await logBookModel.save(lb)
                    getOfflineLogBookEntry()
                */
                }
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        jetCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const saveSectionComment = () => {
        getComment('JetSpecific', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('JetSpecific', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'JetSpecific',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)



    return (
        <>
            <Card className="space-y-6">
                {logBookConfig && vesselDailyCheck && (
                    <CheckField>
                        {getFilteredFields(fields, false, logBookConfig).filter(
                            (field: any) =>
                                displayField(field.name, logBookConfig),
                        ).length > 0 && <CheckFieldTopContent />}
                        <CheckFieldContent>
                            {getFilteredFields(
                                fields,
                                false,
                                logBookConfig,
                            ).map((field: any, index: number) => (
                                <DailyCheckField
                                    locked={!edit_logBookEntry || locked}
                                    key={index}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handleJetSpecificChecks(
                                            false,
                                            field.value,
                                        )
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleJetSpecificChecks(
                                            true,
                                            field.value,
                                        )
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    offline={offline}
                                    onCommentSave={(commentText) =>
                                        handleSaveFieldComment(
                                            field.name,
                                            commentText,
                                        )
                                    }
                                    comment={getComment(field.name)?.comment}
                                    displayImage={true}
                                    fieldImages={fieldImages}
                                    onImageUpload={refreshImages}
                                />
                            ))}

                            {getFilteredFields(fields, true, logBookConfig)
                                ?.filter((groupField: any) =>
                                    displayField(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                ?.map((groupField: any) => (
                                    <>
                                        <>
                                            {groupField.field?.title
                                                ? groupField.field.title
                                                : groupField.field.label}
                                            {displayDescription(
                                                groupField.name,
                                                logBookConfig,
                                            ) && (
                                                <Button
                                                    variant="text"
                                                    size="icon"
                                                    iconLeft={AlertCircle}
                                                    iconOnly
                                                    onClick={() => {
                                                        setDescriptionPanelContent(
                                                            displayDescription(
                                                                groupField.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        setOpenDescriptionPanel(
                                                            true,
                                                        )
                                                        setDescriptionPanelHeading(
                                                            groupField.name,
                                                        )
                                                    }}
                                                />
                                            )}
                                        </>

                                        {groupField?.items?.map(
                                            (field: any, index: number) => (
                                                <DailyCheckField
                                                    locked={
                                                        !edit_logBookEntry ||
                                                        locked
                                                    }
                                                    className="lg:!grid-cols-2"
                                                    innerWrapperClassName="lg:!col-span-1"
                                                    key={index}
                                                    displayField={displayField(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    displayDescription={displayDescription(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    displayLabel={getFieldLabel(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    inputId={field.value}
                                                    handleNoChange={() =>
                                                        handleJetSpecificChecks(
                                                            false,
                                                            field.value,
                                                        )
                                                    }
                                                    defaultNoChecked={
                                                        field.checked ===
                                                        'Not_Ok'
                                                    }
                                                    handleYesChange={() =>
                                                        handleJetSpecificChecks(
                                                            true,
                                                            field.value,
                                                        )
                                                    }
                                                    defaultYesChecked={
                                                        field.checked === 'Ok'
                                                    }
                                                    offline={offline}
                                                    onCommentSave={(
                                                        commentText,
                                                    ) =>
                                                        handleSaveFieldComment(
                                                            field.name,
                                                            commentText,
                                                        )
                                                    }
                                                    comment={
                                                        getComment(field.name)
                                                            ?.comment
                                                    }
                                                    displayImage={true}
                                                    fieldImages={fieldImages}
                                                    onImageUpload={
                                                        refreshImages
                                                    }
                                                />
                                            ),
                                        )}
                                    </>
                                ))}
                        </CheckFieldContent>
                    </CheckField>
                )}

                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="JetCrewResponsible"
                    timeKey="JetCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleJetCrewResponsible}
                    crewResponsible={jetCrewResponsible}
                    checkTime={jetCheckTime}
                    handleCheckTime={handleJetCheckTime}
                    setCheckTime={setJetCheckTime}
                />

                <Label label="Comments" htmlFor="section_comment">
                    <Textarea
                        id="section_comment"
                        rows={4}
                        disabled={!edit_logBookEntry || locked}
                        placeholder="Comments for jet related cheks..."
                        onChange={(e) => {
                            setSectionComment(e.target.value)
                            debounceSaveSectionComment()
                        }}
                        // onBlur={(e) =>

                        // }
                        defaultValue={
                            getComment('JetSpecific', 'Section')?.comment
                        }
                    />
                </Label>
            </Card>

            <AlertDialogNew
                openDialog={openDescriptionPanel}
                setOpenDialog={(open: boolean) => {
                    if (!open) {
                        setOpenDescriptionPanel(false)
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}
                title={`Field - ${descriptionPanelHeading}`}
                cancelText="Close"
                noButton={false}
                actionText="">
                {typeof descriptionPanelContent === 'string' ? (
                    <div
                        className="prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{
                            __html: descriptionPanelContent,
                        }}
                    />
                ) : (
                    <div className="prose prose-sm max-w-none">
                        {descriptionPanelContent}
                    </div>
                )}
            </AlertDialogNew>
        </>
    )
}
