'use client'
import React, { useEffect, useState } from 'react'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import {
    displayField,
    getFieldLabel,
    displayDescription,
    composeField,
    getFilteredFields,
} from '@/app/ui/daily-checks/actions'

import {
    Sheet,
    Sheet<PERSON>ontent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import {
    getHullDayShapes,
    getHullDeckEquipment,
    getHullOtherFields,
    getHullStructureFields,
} from '@/app/lib/dailyCheckFields'
import { useDebounceFn, useMediaQuery } from '@reactuses/core'
import <PERSON><PERSON><PERSON><PERSON> from './crew-checker'
import dayjs from 'dayjs'
import { useSearchParams } from 'next/navigation'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    CREATE_SECTION_MEMBER_COMMENT,
    UPDATE_SECTION_MEMBER_COMMENT,
    UpdateVesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/mutation'
import {
    GET_SECTION_MEMBER_COMMENTS,
    GET_SECTION_MEMBER_IMAGES,
    VesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
// DailyCheckField is already imported at the top
import { Textarea } from '@/components/ui/textarea'
import { AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ActionFooter } from '@/components/ui/action-footer'
import {
    CheckField,
    CheckFieldContent,
    CheckFieldHeader,
    CheckFieldTitle,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { Card } from '@/components/ui/card'
import { Label } from '@/components/ui/label'

export default function Hull({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    // setTab is kept for API compatibility but not used in this component
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages?: any
    refreshImages?: any
}) {
    // Router removed as it's no longer needed with ActionFooter
    const searchParams = useSearchParams()
    // vesselID is kept for potential future use
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [saving, setSaving] = useState(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [sectionComment, setSectionComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    // isWide is kept for potential responsive design improvements
    const isWide = useMediaQuery('(min-width: 640px)')
    const [deckOpsCrewResponsible, setDeckOpsCrewResponsible] = useState<any>(
        vesselDailyCheck?.deckOpsCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )
    const [deckOpsCheckTime, setDeckOpsCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.deckOpsCheckTime ?? new Date()),
    )
    const handleHullChecks = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await dailyCheckModel.save(variables)
                setVesselDailyCheck([data])
                // setSaving(true)
                setSaving(false)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [tab])

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing safety check', error)
            },
        },
    )

    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [])

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }

        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (_response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (_response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const hullStructureFields = getHullStructureFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const hullDeckEquipment = getHullDeckEquipment(
        logBookConfig,
        vesselDailyCheck,
    )

    const hullDayShapes = getHullDayShapes(logBookConfig, vesselDailyCheck)

    const fields = getHullOtherFields(logBookConfig, vesselDailyCheck)

    const handleSave = async () => {
        if (offline) {
            const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
            setVesselDailyCheck(data)
            setSaving(true)
        } else {
            getSectionVesselDailyCheck_LogBookEntrySection({
                variables: {
                    id: [vesselDailyCheck.id],
                },
            })
        }
        const comment = sectionComment
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: 'Hull',
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = getComment('Hull', 'Section')
                    ? getComment('Hull', 'Section').id
                    : generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    // These group change handlers are kept for potential future use with group fields
    // Currently not used as the group field UI has been commented out
    const handleGroupNoChange = (groupField: any, groupFieldParent: any) => {
        handleHullChecks(
            false,
            [
                ...hullStructureFields,
                ...hullDeckEquipment,
                ...hullDayShapes,
                ...fields,
            ].find((field: any) => field.name === groupFieldParent.name)?.value,
        )
        groupField.map((field: any) => handleHullChecks(false, field.value))
    }

    const handleGroupYesChange = (groupField: any, groupFieldParent: any) => {
        handleHullChecks(
            true,
            [
                ...hullStructureFields,
                ...hullDeckEquipment,
                ...hullDayShapes,
                ...fields,
            ].find((field: any) => field.name === groupFieldParent.name)?.value,
        )
        groupField.map((field: any) => handleHullChecks(true, field.value))
    }

    const handleDeckOpsCrewResponsible = async (crews: any) => {
        setDeckOpsCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                deckOpsCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log(' offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleDeckOpsCheckTime = async (date: any) => {
        setDeckOpsCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                deckOpsCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log(' offline')
                /* const newVesselDailyCheck =
                            await dailyCheckModel.save(variables)
                        // setSaving(true)
                        setSaving(false)
                        setVesselDailyCheck([newVesselDailyCheck])
                        const sections = logbook.logBookEntrySections.nodes
                        const section = {
                            className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                            id: `${vesselDailyCheck.id}`,
                            logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                            __typename: 'VesselDailyCheck_LogBookEntrySection',
                        }
                        if (
                            !sections.some(
                                (s: any) =>
                                    JSON.stringify(s) === JSON.stringify(section),
                            )
                        ) {
                            sections.push(section)
                        }
                        const lb = {
                            ...logbook,
                            logBookEntrySections: { nodes: sections },
                        }
                        await logBookModel.save(lb)
                        getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        deckOpsCheckTime:
                            dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const saveSectionComment = () => {
        getComment('Hull', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('Hull', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'Hull',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    return (
        <>
            <Card className="space-y-4">
                <CheckField>
                    {(getFilteredFields(fields, false, logBookConfig).filter(
                        (field: any) => displayField(field.name, logBookConfig),
                    ).length > 0 ||
                        getFilteredFields(
                            hullStructureFields,
                            true,
                            logBookConfig,
                        )?.filter((groupField: any) =>
                            displayField(groupField.name, logBookConfig),
                        ).length > 0 ||
                        getFilteredFields(
                            hullDeckEquipment,
                            true,
                            logBookConfig,
                        )?.filter((groupField: any) =>
                            displayField(groupField.name, logBookConfig),
                        ).length > 0) && <CheckFieldTopContent />}
                    <CheckFieldContent>
                        {getFilteredFields(fields, false, logBookConfig).map(
                            (field: any, index: number) => (
                                <DailyCheckField
                                    key={`hull-field-${field.name}-${field.value}-${index}`}
                                    locked={locked || !edit_logBookEntry}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handleHullChecks(false, field.value)
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleHullChecks(true, field.value)
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment(field.name),
                                            composeField(
                                                field.name,
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment(field.name)?.comment}
                                    displayImage={true}
                                    fieldImages={fieldImages}
                                    onImageUpload={refreshImages}
                                />
                            ),
                        )}

                        {getFilteredFields(
                            hullStructureFields,
                            true,
                            logBookConfig,
                        )?.filter((groupField: any) =>
                            displayField(groupField.name, logBookConfig),
                        ).length > 0 &&
                            logBookConfig &&
                            vesselDailyCheck && (
                                <>
                                    {getFilteredFields(
                                        hullStructureFields,
                                        true,
                                        logBookConfig,
                                    )
                                        ?.filter((groupField: any) =>
                                            displayField(
                                                groupField.name,
                                                logBookConfig,
                                            ),
                                        )
                                        ?.map(
                                            (
                                                groupField: any,
                                                groupIndex: number,
                                            ) => (
                                                <React.Fragment
                                                    key={`hull-structure-group-${groupField.name}-${groupIndex}`}>
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}>
                                                                //     {field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}
                                                                //     {' - '}
                                                                // </span>

                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleHullChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleHullChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                    displayImage={
                                                                        true
                                                                    }
                                                                    fieldImages={
                                                                        fieldImages
                                                                    }
                                                                    onImageUpload={
                                                                        refreshImages
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <Button
                                                            variant="text"
                                                            size="icon"
                                                            iconLeft={
                                                                AlertCircle
                                                            }
                                                            iconOnly
                                                            className="ml-1"
                                                            onClick={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}

                                                    <>
                                                        {/* <DailyCheckGroupField
                                                    locked={
                                                        locked ||
                                                        !edit_logBookEntry
                                                    }
                                                    groupField={groupField?.items?.filter(
                                                        (field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                    )}
                                                    handleYesChange={() =>
                                                        handleGroupYesChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    handleNoChange={() =>
                                                        handleGroupNoChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    defaultNoChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Not_Ok',
                                                        )}
                                                    defaultYesChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Ok',
                                                        )}
                                                    commentAction={() =>
                                                        showCommentPopup(
                                                            getComment(
                                                                groupField.name,
                                                            ),
                                                            composeField(
                                                                groupField.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                    }
                                                    comment={
                                                        getComment(
                                                            groupField.name,
                                                        )?.comment
                                                    }
                                                /> */}
                                                        {/* {groupField?.items?.map(
                                                    (
                                                        field: any,
                                                        index: number,
                                                    ) => (
                                                        <DailyCheckField
                                                            locked={
                                                                locked ||
                                                                !edit_logBookEntry
                                                            }
                                                            className={`lg:!grid-cols-2 hidden`}
                                                            innerWrapperClassName={`lg:!col-span-1`}
                                                            key={index}
                                                            displayField={displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayDescription={displayDescription(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayLabel={getFieldLabel(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            inputId={
                                                                field.value
                                                            }
                                                            handleNoChange={() =>
                                                                handleHullChecks(
                                                                    false,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultNoChecked={
                                                                field.checked ===
                                                                'Not_Ok'
                                                            }
                                                            handleYesChange={() =>
                                                                handleHullChecks(
                                                                    true,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultYesChecked={
                                                                field.checked ===
                                                                'Ok'
                                                            }
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    getComment(
                                                                        field.name,
                                                                    ),
                                                                    composeField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                            }
                                                            comment={
                                                                getComment(
                                                                    field.name,
                                                                )?.comment
                                                            }
                                                        />
                                                    ),
                                                )} */}
                                                    </>
                                                </React.Fragment>
                                            ),
                                        )}
                                </>
                            )}

                        {getFilteredFields(
                            hullDeckEquipment,
                            true,
                            logBookConfig,
                        )?.filter((groupField: any) =>
                            displayField(groupField.name, logBookConfig),
                        ).length > 0 &&
                            logBookConfig &&
                            vesselDailyCheck &&
                            getFilteredFields(
                                hullDeckEquipment,
                                true,
                                logBookConfig,
                            )
                                ?.filter((groupField: any) =>
                                    displayField(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                ?.map((groupField: any, groupIndex: number) => (
                                    <React.Fragment
                                        key={`hull-deck-group-${groupField.name}-${groupIndex}`}>
                                        {groupField?.items
                                            ?.filter((field: any) =>
                                                displayField(
                                                    field.name,
                                                    logBookConfig,
                                                ),
                                            )
                                            ?.map(
                                                (field: any, index: number) => (
                                                    // <span
                                                    //     key={`${field.label}-${index}`}>
                                                    //     {field.label}
                                                    //     {displayDescription(
                                                    //         field.name,
                                                    //         logBookConfig,
                                                    //     ) && (
                                                    //         <SeaLogsButton
                                                    //             icon="alert"
                                                    //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                    //             action={() => {
                                                    //                 setDescriptionPanelContent(
                                                    //                     displayDescription(
                                                    //                         field.name,
                                                    //                         logBookConfig,
                                                    //                     ),
                                                    //                 )
                                                    //                 setOpenDescriptionPanel(
                                                    //                     true,
                                                    //                 )
                                                    //                 setDescriptionPanelHeading(
                                                    //                     field.name,
                                                    //                 )
                                                    //             }}
                                                    //         />
                                                    //     )}
                                                    //     {' - '}
                                                    // </span>
                                                    <DailyCheckField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        key={`hull-deck-field-${field.name}-${field.value}-${index}`}
                                                        displayField={displayField(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        displayDescription={displayDescription(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        setOpenDescriptionPanel={
                                                            setOpenDescriptionPanel
                                                        }
                                                        setDescriptionPanelHeading={
                                                            setDescriptionPanelHeading
                                                        }
                                                        displayLabel={getFieldLabel(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        inputId={field.value}
                                                        handleNoChange={() =>
                                                            handleHullChecks(
                                                                false,
                                                                field.value,
                                                            )
                                                        }
                                                        defaultNoChecked={
                                                            field.checked ===
                                                            'Not_Ok'
                                                        }
                                                        handleYesChange={() =>
                                                            handleHullChecks(
                                                                true,
                                                                field.value,
                                                            )
                                                        }
                                                        defaultYesChecked={
                                                            field.checked ===
                                                            'Ok'
                                                        }
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    field.name,
                                                                ),
                                                                composeField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                field.name,
                                                            )?.comment
                                                        }
                                                        displayImage={true}
                                                        fieldImages={
                                                            fieldImages
                                                        }
                                                        onImageUpload={
                                                            refreshImages
                                                        }
                                                    />
                                                ),
                                            )}
                                        {displayDescription(
                                            groupField.name,
                                            logBookConfig,
                                        ) && (
                                            <Button
                                                variant="text"
                                                size="icon"
                                                iconLeft={AlertCircle}
                                                iconOnly
                                                className="ml-1"
                                                onClick={() => {
                                                    setDescriptionPanelContent(
                                                        displayDescription(
                                                            groupField.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                    setOpenDescriptionPanel(
                                                        true,
                                                    )
                                                    setDescriptionPanelHeading(
                                                        groupField.name,
                                                    )
                                                }}
                                            />
                                        )}
                                    </React.Fragment>
                                ))}
                    </CheckFieldContent>
                </CheckField>
                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="DeckOpsCrewResponsible"
                    timeKey="DeckOpsCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleDeckOpsCrewResponsible}
                    crewResponsible={deckOpsCrewResponsible}
                    checkTime={deckOpsCheckTime}
                    handleCheckTime={handleDeckOpsCheckTime}
                    setCheckTime={setDeckOpsCheckTime}
                />
                <Label label="Comments">
                    <Textarea
                        id={`section_comment`}
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        placeholder="Comments on deck operations & exterior..."
                        onChange={(e) => {
                            setSectionComment(e.target.value)
                            debounceSaveSectionComment()
                        }}
                        // onBlur={(e) =>
                        //     saveSectionComment()
                        // }
                        defaultValue={getComment('Hull', 'Section')?.comment}
                    />
                </Label>
            </Card>
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                actionText="Save"
                size="md">
                <Textarea
                    id="comment"
                    readOnly={locked || !edit_logBookEntry}
                    disabled={locked || !edit_logBookEntry}
                    rows={4}
                    className="mt-4 w-full"
                    placeholder="Comment"
                    defaultValue={currentComment ? currentComment.comment : ''}
                />
            </AlertDialogNew>
            <Sheet
                open={openDescriptionPanel}
                onOpenChange={(open) => {
                    setOpenDescriptionPanel(open)
                    if (!open) {
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}>
                <SheetContent side="left">
                    <SheetHeader>
                        <SheetTitle>
                            Field -{' '}
                            <span className="font-normal">
                                {descriptionPanelHeading}
                            </span>
                        </SheetTitle>
                    </SheetHeader>
                    <div className="mt-6 overflow-y-auto">
                        <div
                            className="prose prose-sm max-w-none"
                            dangerouslySetInnerHTML={{
                                __html: descriptionPanelContent,
                            }}
                        />
                    </div>
                </SheetContent>
            </Sheet>
        </>
    )
}
