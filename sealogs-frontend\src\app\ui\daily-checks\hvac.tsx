'use client'

import React, { useEffect, useState } from 'react'
// Apollo Client
import { useMutation, useLazyQuery } from '@apollo/client'
// GraphQL
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import {
    GET_SECTION_MEMBER_COMMENTS,
    GET_SECTION_MEMBER_IMAGES,
    VesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
// Next.js
import { useSearchParams } from 'next/navigation'
// Hooks
import { useDebounceFn, useMediaQuery } from '@reactuses/core'
// Utilities
import {
    displayDescription,
    composeField,
    displayField,
    getFilteredFields,
    getSortOrder,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'
import { generateUniqueId } from '@/app/offline/helpers/functions'
// Models
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
// Components
import {
    CheckField,
    CheckFieldTopContent,
    CheckFieldContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import CrewChecker from './crew-checker'
import { Label } from '@/components/ui/label'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { Textarea } from '@/components/ui/textarea'
// Other
import dayjs from 'dayjs'
import 'react-quill/dist/quill.snow.css'
import { Card } from '@/components/ui'

export default function Hvac({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages: any
    refreshImages: any
}) {
    // Router removed as it's no longer needed with ActionFooter
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [saving, setSaving] = useState(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    // Used in the textarea onChange handler on line 664
    const [sectionComment, setSectionComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')

    // These state variables were previously used to track the status of each check field
    // They are no longer needed as the state is managed in the parent component via vesselDailyCheck
    // and the new DailyCheckField component handles its own internal state
    /*
    const [hvac, setHvac] = useState<Boolean>(vesselDailyCheck?.hvac === 'Ok')
    const [tv, setTv] = useState<Boolean>(vesselDailyCheck?.tv === 'Ok')
    const [stabilizationSystems, setStabilizationSystems] = useState<Boolean>(
        vesselDailyCheck?.stabilizationSystems === 'Ok',
    )
    const [electronics, setElectronics] = useState<Boolean>(
        vesselDailyCheck?.electronics === 'Ok',
    )
    */
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState<
        string | React.ReactNode
    >('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    const isWide = useMediaQuery('(min-width: 640px)')

    const [hvacCrewResponsible, setHVACCrewResponsible] = useState<any>(
        vesselDailyCheck?.hvacCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )
    const [hvacCheckTime, setHVACCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.hvacCheckTime ?? new Date()),
    )

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [tab])

    const handleHvacChecks = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const variable = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await dailyCheckModel.save(variable)
                setVesselDailyCheck([data])
                // setSaving(true)
                setSaving(false)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variable,
                    },
                })
            }
        }
    }

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [])

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing safety check', error)
            },
        },
    )

    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const fields = [
        {
            name: 'HVAC',
            label: 'HVAC',
            value: 'hvac',
            checked: vesselDailyCheck?.hvac,
            sortOrder: getSortOrder('HVAC', logBookConfig),
        },
        {
            name: 'TV',
            label: 'TV',
            value: 'tv',
            checked: vesselDailyCheck?.tv, // Changed from currentValue to checked
            sortOrder: getSortOrder('TV', logBookConfig),
        },
        {
            name: 'StabilizationSystems',
            label: 'Stabilization systems',
            value: 'stabilizationSystems',
            checked: vesselDailyCheck?.stabilizationSystems, // Changed from currentValue to checked
            sortOrder: getSortOrder('StabilizationSystems', logBookConfig),
        },
        {
            name: 'Electronics',
            label: 'Electronics',
            value: 'electronics',
            checked: vesselDailyCheck?.electronics, // Changed from currentValue to checked
            sortOrder: getSortOrder('Electronics', logBookConfig),
        },
    ]

    const handleHVACCrewResponsible = async (crews: any) => {
        setHVACCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                hvacCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleHVACCheckTime = async (date: any) => {
        setHVACCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                hvacCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('offline')
                /* const newVesselDailyCheck =
                                            await dailyCheckModel.save(variables)
                                        // setSaving(true)
                                        setSaving(false)
                                        setVesselDailyCheck([newVesselDailyCheck])
                                        const sections = logbook.logBookEntrySections.nodes
                                        const section = {
                                            className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                                            id: `${vesselDailyCheck.id}`,
                                            logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                                            __typename: 'VesselDailyCheck_LogBookEntrySection',
                                        }
                                        if (
                                            !sections.some(
                                                (s: any) =>
                                                    JSON.stringify(s) === JSON.stringify(section),
                                            )
                                        ) {
                                            sections.push(section)
                                        }
                                        const lb = {
                                            ...logbook,
                                            logBookEntrySections: { nodes: sections },
                                        }
                                        await logBookModel.save(lb)
                                        getOfflineLogBookEntry() */
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const saveSectionComment = () => {
        getComment('HVAC', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('HVAC', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'HVAC',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)



    return (
        <>
            <Card className="space-y-6">
                {logBookConfig && vesselDailyCheck && (
                    <CheckField>
                        {getFilteredFields(fields, false, logBookConfig).filter(
                            (field: any) =>
                                displayField(field.name, logBookConfig),
                        ).length > 0 && <CheckFieldTopContent />}
                        <CheckFieldContent>
                            {getFilteredFields(
                                fields,
                                false,
                                logBookConfig,
                            ).map((field: any, index: number) => (
                                <DailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    key={index}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    // Support both internal and external description panel
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handleHvacChecks(false, field.value)
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleHvacChecks(true, field.value)
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment(field.name),
                                            composeField(
                                                field.name,
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment(field.name)?.comment}
                                    offline={offline}
                                    displayImage={true}
                                    fieldImages={fieldImages}
                                    onImageUpload={refreshImages}
                                />
                            ))}
                        </CheckFieldContent>
                    </CheckField>
                )}

                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="HVACCrewResponsible"
                    timeKey="HVACCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleHVACCrewResponsible}
                    crewResponsible={hvacCrewResponsible}
                    checkTime={hvacCheckTime}
                    handleCheckTime={handleHVACCheckTime}
                    setCheckTime={setHVACCheckTime}
                />

                <Label label="Comments">
                    <Textarea
                        id="section_comment"
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        placeholder="Comments for HVAC & general equipment..."
                        onChange={(e) => {
                            setSectionComment(e.target.value)
                            debounceSaveSectionComment()
                        }}
                        defaultValue={getComment('HVAC', 'Section')?.comment}
                    />
                </Label>
            </Card>

            {/* <ActionFooter
                showFooter={!locked || edit_logBookEntry}
                onCreateTask={handleCreateTask}
                onSave={handleSave}
                createTaskLoading={createMaintenanceCheckLoading}
                createTaskDisabled={createMaintenanceCheckLoading}
            /> */}

            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                title="Comment"
                actionText="Save">
                <div>
                    <Label>Comment</Label>
                    <Textarea
                        readOnly={locked || !edit_logBookEntry}
                        disabled={locked || !edit_logBookEntry}
                        id="comment"
                        rows={4}
                        className="mt-4"
                        placeholder="Comment"
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }
                    />
                </div>
            </AlertDialogNew>

            {/*
              The external Sheet component is kept for backward compatibility,
              but the new DailyCheckField component has its own internal Sheet component
              that will be used when clicking on the info button.
            */}
            <Sheet
                open={openDescriptionPanel}
                onOpenChange={(open) => {
                    setOpenDescriptionPanel(open)
                    if (!open) {
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}>
                <SheetContent
                    side="left"
                    className={`${isWide ? 'w-[60vw]' : 'w-[90vw]'} p-0 overflow-y-auto`}>
                    <div className="h-full flex flex-col p-6">
                        <SheetHeader>
                            <SheetTitle>
                                Field -{' '}
                                <span className="font-light">
                                    {descriptionPanelHeading}
                                </span>
                            </SheetTitle>
                        </SheetHeader>

                        <div className="flex-grow mt-6 overflow-y-auto">
                            {typeof descriptionPanelContent === 'string' ? (
                                <div
                                    className="prose prose-sm max-w-none"
                                    dangerouslySetInnerHTML={{
                                        __html: descriptionPanelContent as string,
                                    }}
                                />
                            ) : (
                                <div className="prose prose-sm max-w-none">
                                    {descriptionPanelContent}
                                </div>
                            )}
                        </div>
                    </div>
                </SheetContent>
            </Sheet>
        </>
    )
}
