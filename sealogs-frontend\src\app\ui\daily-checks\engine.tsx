'use client'
import React, { useCallback, useEffect, useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import {
    UpdateFuelTank,
    UpdateVesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/mutation'
import {
    GET_ENGINES,
    GET_FUELTANKS,
    GET_LOGBOOK_ENTRY_BY_ID,
    GET_SECTION_MEMBER_COMMENTS,
    LogBookSignOff_LogBookEntrySection,
    TripReport_LogBookEntrySection,
    VesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { useSearchParams } from 'next/navigation'
import {
    Sheet,
    Sheet<PERSON>ontent,
    She<PERSON><PERSON>eader,
    Sheet<PERSON>it<PERSON>,
} from '@/components/ui/sheet'

import { getVesselByID } from '@/app/lib/actions'
import dayjs from 'dayjs'
import 'react-quill/dist/quill.snow.css'
import VesselModel from '@/app/offline/models/vessel'
import EngineModel from '@/app/offline/models/engine'
import FuelTankModel from '@/app/offline/models/fuelTank'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import LogBookSignOff_LogBookEntrySectionModel from '@/app/offline/models/logBookSignOff_LogBookEntrySection'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { useMediaQuery } from '@reactuses/core'
import { Textarea } from '@/components/ui/textarea'
import { ChevronRight } from 'lucide-react'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { ActionFooter } from '@/components/ui/action-footer'
import { TabItem } from '@/components/ui/custom-tab'
import {
    OtherEngineChecks,
    PostStartupChecks,
    PreStartupChecks,
    EngineeringCheckFields,
    useEngineFields,
} from './engine-checks'
import ResponsiveTab from '@/components/ui/responsive-tab'

export default function DailyEngineChecks({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    updateTripReport,
    setComments,
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    updateTripReport?: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages: any
    refreshImages: any
}) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [saving, setSaving] = useState(false)
    const [loaded, setLoaded] = useState(true)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [sectionComment, setSectionComment] = useState<any>('')
    const [sectionEngineComment, setSectionEngineComment] = useState<any>('')
    const [sectionFuelComment, setSectionFuelComment] = useState<any>('')
    const [vessel, setVessel] = useState<any>(false)
    const [prevReport, setPrevReport] = useState<any>(null)
    const [prevSignOff, setPrevSignOff] = useState<any>(null)
    const [fuelTankList, setFuelTankList] = useState<any>(null)
    const [fuelLevel, setFuelLevel] = useState<any>(false)
    const [prevFuelLevel, setPrevFuelLevel] = useState<any>(false)
    const [currentReport, setCurrentReport] = useState<any>(null)
    const [engineList, setEngineList] = useState<any>(null)
    const [signOff, setSignOff] = useState<any>(null)
    const [entryLastCreated, setEntryLastCreated] = useState<any>(false)
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const [fuelLogs, setFuelLogs] = useState<any>([])
    const [additionalComment, setAdditionalComment] = useState<Boolean>(false)
    const vesselModel = new VesselModel()
    const engineModel = new EngineModel()
    const fuelTankModel = new FuelTankModel()
    const logbookModel = new LogBookEntryModel()
    const commentModel = new SectionMemberCommentModel()
    const signOffModel = new LogBookSignOff_LogBookEntrySectionModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    const isWide = useMediaQuery('(min-width: 640px)')

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [tab])

    const handleSetLogbook = (logbook: any) => {
        setFuelLogs(logbook.fuelLog.nodes)
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        sectionTypes.forEach(async (section: any) => {
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await tripReportModel.getByIds(section.ids)
                    setCurrentReport(data)
                } else {
                    getSectionTripReport_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            if (
                section.className ===
                'SeaLogs\\LogBookSignOff_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await signOffModel.getByIds(section.ids)
                    setSignOff(data)
                } else {
                    getLogBookSignOff_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
        })
    }

    const [getLogBookSignOff_LogBookEntrySection] = useLazyQuery(
        LogBookSignOff_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readLogBookSignOff_LogBookEntrySections.nodes
                setSignOff(data)
            },
            onError: (error: any) => {
                console.error('LogBookSignOff_LogBookEntrySection error', error)
            },
        },
    )

    const [getSectionTripReport_LogBookEntrySection] = useLazyQuery(
        TripReport_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                setCurrentReport(data)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const loadLogBookEntry = async (id: any) => {
        if (offline) {
            const data = await logbookModel.getById(id)
            if (data) {
                handleSetLogbook(data)
            }
        } else {
            await queryCurrentLogBookEntry({
                variables: {
                    logbookEntryId: +id,
                },
            })
        }
    }

    const [queryCurrentLogBookEntry] = useLazyQuery(GET_LOGBOOK_ENTRY_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    const handleSetVesselInfo = async (vesselInfo: any) => {
        const prevLockedEntry = vesselInfo?.logBookEntries.nodes.find(
            (entry: any) => entry.state === 'Locked',
        )

        if (prevLockedEntry) {
            if (offline) {
                const data = await logbookModel.getById(prevLockedEntry.id)
                if (data) {
                    handleSetPrevLogbook(data)
                }
            } else {
                queryLogBookEntry({
                    variables: {
                        logbookEntryId: +prevLockedEntry.id,
                    },
                })
            }
        }
        if (vesselInfo?.logBookEntries?.nodes[0].id) {
            loadLogBookEntry(vesselInfo?.logBookEntries?.nodes[0].id)
        }
        const fuelTankIds = vesselInfo?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'FuelTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        fuelTankIds?.length > 0 && getFuelTanks(fuelTankIds)
        const engineIds =
            vesselInfo?.parentComponent_Components?.nodes
                .filter(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'Engine',
                )
                .map((item: any) => {
                    return item.basicComponent.id
                }) ?? []
        engineIds.length > 0 && getEngines(engineIds)
        setVessel(vesselInfo)
    }

    const getEngines = async (engineIds: any) => {
        if (offline) {
            let engines = await engineModel.getByIds(engineIds)
            engines = engines.map((engine: any) => {
                const filteredNodes = engine.engineStartStops.nodes.filter(
                    (node: any) =>
                        node.logBookEntrySection.logBookEntryID ===
                        `${logentryID}`,
                )
                return {
                    ...engine,
                    engineStartStops: filteredNodes,
                }
            })
            setEngineList(engines)
        } else {
            await queryGetEngines({
                variables: {
                    id: engineIds,
                    filter: {
                        logBookEntrySection: {
                            logBookEntryID: { eq: +logentryID },
                        },
                    },
                },
            })
        }
    }

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngineList(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelTankIds)
            setFuelTankList(data)
        } else {
            await queryGetFuelTanks({
                variables: {
                    id: fuelTankIds,
                },
            })
        }
    }

    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelTanks.nodes
            setFuelTankList(data)
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const handleSetPrevLogbook = (logbook: any) => {
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        sectionTypes.forEach(async (section: any) => {
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await tripReportModel.getByIds(section.ids)
                    setPrevReport(data)
                } else {
                    getSectionPrevTripReport_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            if (
                section.className ===
                'SeaLogs\\LogBookSignOff_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await signOffModel.getByIds(section.ids)
                    setPrevSignOff(data)
                } else {
                    getLogBookPrevSignOff_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
        })
    }

    const [getSectionPrevTripReport_LogBookEntrySection] = useLazyQuery(
        TripReport_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                setPrevReport(data)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const [getLogBookPrevSignOff_LogBookEntrySection] = useLazyQuery(
        LogBookSignOff_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readLogBookSignOff_LogBookEntrySections.nodes
                setPrevSignOff(data)
            },
            onError: (error: any) => {
                console.error('LogBookSignOff_LogBookEntrySection error', error)
            },
        },
    )

    const [queryLogBookEntry] = useLazyQuery(GET_LOGBOOK_ENTRY_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetPrevLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    if (!offline) {
        getVesselByID(+vesselID, handleSetVesselInfo)
    }

    // const loadVessel = async () => {
    //     const vessel = await vesselModel.getById(vesselID)
    //     handleSetVesselInfo(vessel)
    // }
    // useEffect(() => {
    //     loadVessel()
    // }, [])
    useEffect(() => {
        if (currentReport) {
            var fuelLevel = 0
            var created = 0
            currentReport.forEach((report: any) => {
                report.tripEvents.nodes?.forEach((event: any) => {
                    if (event.eventCategory === 'PassengerDropFacility') {
                        fuelLevel =
                            +event.eventType_PassengerDropFacility.fuelLevel > 0
                                ? event.eventType_PassengerDropFacility
                                      .fuelLevel
                                : fuelLevel
                        created = dayjs(event.created).isAfter(
                            dayjs(entryLastCreated),
                        )
                            ? event.created
                            : created
                    }
                    if (event.eventCategory === 'Tasking') {
                        fuelLevel =
                            +event.eventType_Tasking.fuelLevel > 0
                                ? event.eventType_Tasking.fuelLevel
                                : fuelLevel
                        created = dayjs(event.created).isAfter(
                            dayjs(entryLastCreated),
                        )
                            ? event.created
                            : created
                    }
                    if (event.eventCategory === 'PassengerDropFacility') {
                        fuelLevel =
                            +event.eventType_PassengerDropFacility.fuelLevel > 0
                                ? event.eventType_PassengerDropFacility
                                      .fuelLevel
                                : fuelLevel
                        created = dayjs(event.created).isAfter(
                            dayjs(entryLastCreated),
                        )
                            ? event.created
                            : created
                    }
                })
            })
            setEntryLastCreated(created)
            setFuelLevel(fuelLevel)
        }
    }, [currentReport])

    useEffect(() => {
        if (prevSignOff) {
            var fuelLevel = 0
            {
                prevSignOff[0].fuelStart > 0
                    ? setPrevFuelLevel(prevSignOff[0].fuelStart)
                    : (prevReport?.forEach((report: any) => {
                          report.tripEvents.nodes?.forEach((event: any) => {
                              if (
                                  event.eventCategory ===
                                  'PassengerDropFacility'
                              ) {
                                  fuelLevel =
                                      event.eventType_PassengerDropFacility
                                          .fuelLevel > 0
                                          ? event
                                                .eventType_PassengerDropFacility
                                                .fuelLevel
                                          : fuelLevel
                              }
                              if (event.eventCategory === 'Tasking') {
                                  fuelLevel =
                                      event.eventType_Tasking.fuelLevel > 0
                                          ? event.eventType_Tasking.fuelLevel
                                          : fuelLevel
                              }
                              if (
                                  event.eventCategory ===
                                  'PassengerDropFacility'
                              ) {
                                  fuelLevel =
                                      event.eventType_PassengerDropFacility
                                          .fuelLevel > 0
                                          ? event
                                                .eventType_PassengerDropFacility
                                                .fuelLevel
                                          : fuelLevel
                              }
                          })
                      }),
                      setPrevFuelLevel(fuelLevel))
            }
        }
    }, [prevSignOff])

    const handleEngineChecks = useCallback(
        async (check: boolean, value: any) => {
            if (+vesselDailyCheck?.id > 0) {
                const data = {
                    id: vesselDailyCheck.id,
                    [value]: check ? 'Ok' : 'Not_Ok',
                    logBookEntryID: logentryID,
                }

                if (offline) {
                    const response = await dailyCheckModel.save(data)
                    setVesselDailyCheck([response])
                    // setSaving(true)
                    setSaving(false)
                } else {
                    setVesselDailyCheck([
                        {
                            ...vesselDailyCheck,
                            [value]: check ? 'Ok' : 'Not_Ok',
                        },
                    ])
                    updateVesselDailyCheck_LogBookEntrySection({
                        variables: {
                            input: data,
                        },
                    })
                }
            }
        },
        [vesselDailyCheck, offline, logentryID],
    )

    // Previously propulsion

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing engine check', error)
            },
        },
    )

    const {
        preEngineFields,
        preEngineOilFields,
        preEngineMountFields,
        preElectricalFields,
        preElectricalVisualFields,
        preFields,
        postEngineFields,
        postEngineStrainersFields,
        postElectricalFields,
        postSteeringFields,
        otherDriveShaftFields,
        otherEngineFieldFields,
        otherMainEngineCheckFields,
        otherEngineRoomVisualInspectionFields,
        otherFuelSystemsFields,
        otherPropulsionCheckFields,
        engrMechanicalFields,
        engrGeneratorFields,
        engrElectronicsFields,
        engrTowlineWinchFields,
    } = useEngineFields(logBookConfig, vesselDailyCheck)

    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
                let notificationFields: any = []
                if (displayTab('Pre-startup checks', logBookConfig)) {
                    notificationFields = [
                        ...preEngineFields,
                        ...preEngineOilFields,
                        ...preEngineMountFields,
                        ...preElectricalFields,
                        ...preElectricalVisualFields,
                        ...preFields,
                    ]
                }
                if (displayTab('Post-startup checks', logBookConfig)) {
                    notificationFields = [
                        ...postEngineFields,
                        ...postEngineStrainersFields,
                        ...postElectricalFields,
                        ...postSteeringFields,
                    ]
                }
                if (displayTab('Other engine checks', logBookConfig)) {
                    notificationFields = [
                        ...otherDriveShaftFields,
                        ...otherEngineFieldFields,
                        ...otherMainEngineCheckFields,
                        ...otherEngineRoomVisualInspectionFields,
                        ...otherFuelSystemsFields,
                        ...otherPropulsionCheckFields,
                    ]
                }
                if (displayTab('Engineering', logBookConfig)) {
                    notificationFields = [
                        ...engrMechanicalFields,
                        ...engrGeneratorFields,
                        ...engrElectronicsFields,
                        ...engrTowlineWinchFields,
                    ]
                }
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )

    useEffect(() => {
        let notificationFields: any = []
        if (displayTab('Pre-startup checks', logBookConfig)) {
            notificationFields = [
                ...preEngineFields,
                ...preEngineOilFields,
                ...preEngineMountFields,
                ...preElectricalFields,
                ...preElectricalVisualFields,
                ...preFields,
            ]
        }
        if (displayTab('Post-startup checks', logBookConfig)) {
            notificationFields = [
                ...postEngineFields,
                ...postEngineStrainersFields,
                ...postElectricalFields,
                ...postSteeringFields,
            ]
        }
        if (displayTab('Other engine checks', logBookConfig)) {
            notificationFields = [
                ...otherDriveShaftFields,
                ...otherEngineFieldFields,
                ...otherMainEngineCheckFields,
                ...otherEngineRoomVisualInspectionFields,
                ...otherFuelSystemsFields,
                ...otherPropulsionCheckFields,
            ]
        }
        if (displayTab('Engineering', logBookConfig)) {
            notificationFields = [
                ...engrMechanicalFields,
                ...engrGeneratorFields,
                ...engrElectronicsFields,
                ...engrTowlineWinchFields,
            ]
        }
    }, [vesselDailyCheck])

    const getComment = useCallback(
        (fieldName: string, commentType = 'FieldComment') => {
            const comment =
                comments?.length > 0
                    ? comments.filter(
                          (comment: any) =>
                              comment.fieldName === fieldName &&
                              comment.commentType === commentType,
                      )
                    : false
            return comment.length > 0 ? comment[0] : false
        },
        [comments],
    )

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        const offlineID = currentComment?.id
            ? currentComment?.id
            : generateUniqueId()
        if (currentComment) {
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const handleSave = async () => {
        if (offline) {
            const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
            setVesselDailyCheck(data)
            setSaving(true)
            let notificationFields: any = []
            if (displayTab('Pre-startup checks', logBookConfig)) {
                notificationFields = [
                    ...preEngineFields,
                    ...preEngineOilFields,
                    ...preEngineMountFields,
                    ...preElectricalFields,
                    ...preElectricalVisualFields,
                    ...preFields,
                ]
            }
            if (displayTab('Post-startup checks', logBookConfig)) {
                notificationFields = [
                    ...postEngineFields,
                    ...postEngineStrainersFields,
                    ...postElectricalFields,
                    ...postSteeringFields,
                ]
            }
            if (displayTab('Other engine checks', logBookConfig)) {
                notificationFields = [
                    ...otherDriveShaftFields,
                    ...otherEngineFieldFields,
                    ...otherMainEngineCheckFields,
                    ...otherEngineRoomVisualInspectionFields,
                    ...otherFuelSystemsFields,
                    ...otherPropulsionCheckFields,
                ]
            }
            if (displayTab('Engineering', logBookConfig)) {
                notificationFields = [
                    ...engrMechanicalFields,
                    ...engrGeneratorFields,
                    ...engrElectronicsFields,
                    ...engrTowlineWinchFields,
                ]
            }
        } else {
            getSectionVesselDailyCheck_LogBookEntrySection({
                variables: {
                    id: [vesselDailyCheck.id],
                },
            })
        }
        const variablesEngine = {
            id: getComment('DailyCheckEngine', 'Section')
                ? getComment('DailyCheckEngine', 'Section').id
                : 0,
            fieldName: 'DailyCheckEngine',
            comment: sectionEngineComment
                ? sectionEngineComment
                : getComment('DailyCheckEngine', 'Section').comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        const variablesFuel = {
            id: getComment('DailyCheckFuel', 'Section')
                ? getComment('DailyCheckFuel', 'Section').id
                : 0,
            fieldName: 'DailyCheckFuel',
            comment: sectionFuelComment
                ? sectionFuelComment
                : getComment('DailyCheckFuel', 'Section').comment,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }

        const fuelStart = (
            document.getElementById('fuel-start') as HTMLInputElement
        )?.value
        if (fuelStart !== fuelLevel ? fuelLevel : prevFuelLevel) {
            Promise.all(
                fuelTankList.map(async (fuelTank: any, index: number) => {
                    const fuelData = {
                        id: fuelTank.id,
                        currentLevel:
                            +fuelStart - fuelTank.capacity * index >
                            fuelTank.capacity
                                ? fuelTank.capacity
                                : +fuelStart - fuelTank.capacity * index > -1
                                  ? +fuelStart - fuelTank.capacity * index
                                  : 0,
                    }
                    if (offline) {
                        await fuelTankModel.save(fuelData)
                        setLoaded(true)
                    } else {
                        updateFuelTank({
                            variables: {
                                input: fuelData,
                            },
                        })
                    }
                }),
            )
        }
        if (getComment('DailyCheckEngine', 'Section')?.id) {
            if (offline) {
                await commentModel.save(variablesEngine)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variablesEngine },
                })
            }
        } else {
            if (offline) {
                const id = getComment('DailyCheckEngine', 'Section')
                    ? getComment('DailyCheckEngine', 'Section').id
                    : generateUniqueId()
                await commentModel.save({ ...variablesEngine, id: id })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variablesEngine },
                })
            }
        }
        if (getComment('DailyCheckFuel', 'Section')?.id) {
            if (offline) {
                await commentModel.save(variablesFuel)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variablesFuel },
                })
            }
        } else {
            if (offline) {
                const id = getComment('DailyCheckFuel', 'Section')
                    ? getComment('DailyCheckFuel', 'Section').id
                    : generateUniqueId()
                await commentModel.save({
                    ...variablesFuel,
                    id: id,
                })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variablesFuel },
                })
            }
        }
    }

    const [updateFuelTank] = useMutation(UpdateFuelTank, {
        onCompleted: (response) => {
            setLoaded(true)
        },
        onError: (error) => {
            console.error('Error updating fuel tank', error)
        },
    })

    const maxCapacity = fuelTankList?.reduce(
        (total: number, tank: any) => total + tank.capacity,
        0,
    )

    const handleGroupYesChange = async (
        groupField: any,
        groupFieldParent: any,
    ) => {
        await handleEngineChecks(
            true,
            [
                ...preEngineFields,
                ...preEngineOilFields,
                ...preEngineMountFields,
                ...preElectricalFields,
                ...preElectricalVisualFields,
                ...preFields,
                ...postEngineFields,
                ...postEngineStrainersFields,
                ...postElectricalFields,
                ...postSteeringFields,
                ...otherDriveShaftFields,
                ...otherEngineFieldFields,
                ...otherMainEngineCheckFields,
                ...otherEngineRoomVisualInspectionFields,
                ...otherFuelSystemsFields,
                ...otherPropulsionCheckFields,
                ...engrMechanicalFields,
                ...engrGeneratorFields,
                ...engrElectronicsFields,
                ...engrTowlineWinchFields,
            ].find((field: any) => field.name === groupFieldParent.name)?.value,
        )
        groupField.map((field: any) => handleEngineChecks(true, field.value))
    }

    const handleGroupNoChange = async (
        groupField: any,
        groupFieldParent: any,
    ) => {
        await handleEngineChecks(
            false,
            [
                ...preEngineFields,
                ...preEngineOilFields,
                ...preEngineMountFields,
                ...preElectricalFields,
                ...preElectricalVisualFields,
                ...preFields,
                ...postEngineFields,
                ...postEngineStrainersFields,
                ...postElectricalFields,
                ...postSteeringFields,
                ...otherDriveShaftFields,
                ...otherEngineFieldFields,
                ...otherMainEngineCheckFields,
                ...otherEngineRoomVisualInspectionFields,
                ...otherFuelSystemsFields,
                ...otherPropulsionCheckFields,
                ...engrMechanicalFields,
                ...engrGeneratorFields,
                ...engrElectronicsFields,
                ...engrTowlineWinchFields,
            ].find((field: any) => field.name === groupFieldParent.name)?.value,
        )
        groupField.map((field: any) => handleEngineChecks(false, field.value))
    }

    const calculatedFuelLevel = fuelTankList?.find((tank: any) =>
        dayjs(tank.lastEdited).isAfter(dayjs(entryLastCreated)),
    )
        ? fuelTankList.reduce(
              (total: number, tank: any) => total + tank.currentLevel,
              0,
          )
        : fuelLevel
          ? fuelLevel
          : prevFuelLevel

    const displayTab = (tab: string, logBookConfig: any): boolean => {
        // Get the VesselDailyCheck_LogBookComponent from the configuration
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'VesselDailyCheck_LogBookComponent',
            )

        return dailyChecks[0]?.subFields?.split('||').includes(tab)
            ? true
            : false
    }

    // Define tab items
    const getTabItems = (): TabItem[] => {
        const tabs: TabItem[] = []

        if (displayTab('Pre-startup checks', logBookConfig)) {
            tabs.push({
                id: 'pre-startup',
                value: 'pre-startup',
                label: 'Pre-startup checks',
                icon: <ChevronRight className="h-5 w-5" />,
                component: (
                    <PreStartupChecks
                        logBookConfig={logBookConfig}
                        updateTripReport={updateTripReport}
                        vesselDailyCheck={vesselDailyCheck}
                        offline={offline}
                        loaded={loaded}
                        locked={locked}
                        edit_logBookEntry={edit_logBookEntry}
                        fuelTankList={fuelTankList}
                        setFuelTankList={setFuelTankList}
                        fuelLogs={fuelLogs}
                        setFuelLogs={setFuelLogs}
                        getFuelTanks={getFuelTanks}
                        engineList={engineList}
                        getComment={getComment}
                        setSectionEngineComment={setSectionEngineComment}
                        createSectionMemberComment={createSectionMemberComment}
                        setSectionFuelComment={setSectionFuelComment}
                        updateSectionMemberComment={updateSectionMemberComment}
                        handleEngineChecks={handleEngineChecks}
                        logentryID={logentryID}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        setDescriptionPanelContent={setDescriptionPanelContent}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        showCommentPopup={showCommentPopup}
                        setComments={setComments}
                        fieldImages={fieldImages}
                        refreshImages={refreshImages}
                    />
                ),
            })
        }

        if (displayTab('Post-startup checks', logBookConfig)) {
            tabs.push({
                id: 'post-startup',
                value: 'post-startup',
                label: 'Post-startup checks',
                icon: <ChevronRight className="h-5 w-5" />,
                component: (
                    <PostStartupChecks
                        edit_logBookEntry={edit_logBookEntry}
                        getComment={getComment}
                        handleEngineChecks={handleEngineChecks}
                        locked={locked}
                        logBookConfig={logBookConfig}
                        logentryID={logentryID}
                        setDescriptionPanelContent={setDescriptionPanelContent}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        showCommentPopup={showCommentPopup}
                        updateVesselDailyCheck_LogBookEntrySection={
                            updateVesselDailyCheck_LogBookEntrySection
                        }
                        vesselDailyCheck={vesselDailyCheck}
                        offline={offline}
                        fieldImages={fieldImages}
                        refreshImages={refreshImages}
                    />
                ),
            })
        }

        if (displayTab('Other engine checks', logBookConfig)) {
            tabs.push({
                id: 'other-engine',
                value: 'other-engine',
                label: 'Other engine checks',
                icon: <ChevronRight className="h-5 w-5" />,
                component: (
                    <OtherEngineChecks
                        edit_logBookEntry={edit_logBookEntry}
                        getComment={getComment}
                        handleEngineChecks={handleEngineChecks}
                        locked={locked}
                        logBookConfig={logBookConfig}
                        logentryID={logentryID}
                        setDescriptionPanelContent={() => {}}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        showCommentPopup={showCommentPopup}
                        updateVesselDailyCheck_LogBookEntrySection={
                            updateVesselDailyCheck_LogBookEntrySection
                        }
                        vesselDailyCheck={vesselDailyCheck}
                        offline={offline}
                        fieldImages={fieldImages}
                        refreshImages={refreshImages}
                    />
                ),
            })
        }

        if (displayTab('Engineering', logBookConfig)) {
            tabs.push({
                id: 'engineering',
                value: 'engineering',
                label: 'Engineering',
                icon: <ChevronRight className="h-5 w-5" />,
                component: (
                    <EngineeringCheckFields
                        logentryID={logentryID}
                        logBookConfig={logBookConfig}
                        vesselDailyCheck={vesselDailyCheck}
                        locked={locked}
                        edit_logBookEntry={edit_logBookEntry}
                        offline={offline}
                        getComment={getComment}
                        handleEngineChecks={handleEngineChecks}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        setDescriptionPanelContent={setDescriptionPanelContent}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        showCommentPopup={showCommentPopup}
                        updateVesselDailyCheck_LogBookEntrySection={
                            updateVesselDailyCheck_LogBookEntrySection
                        }
                        fieldImages={fieldImages}
                        refreshImages={refreshImages}
                    />
                ),
            })
        }

        return tabs
    }

    // Get tab items
    const tabItems = getTabItems()

    return (
        <>
            <ResponsiveTab
                tabs={tabItems}
                isSecHeading
                queryParam="engineTabs"
                tabsTriggerClassName="whitespace-nowrap"
            />
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                cancelText="Cancel"
                actionText="Save">
                <div
                    className={`flex flex-col ${locked || !edit_logBookEntry ? 'pointer-events-none' : ''}`}>
                    <Textarea
                        id="comment"
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        placeholder="Comment"
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }></Textarea>
                </div>
            </AlertDialogNew>
            <Sheet
                open={openDescriptionPanel}
                onOpenChange={(open) => {
                    setOpenDescriptionPanel(open)
                    if (!open) {
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}>
                <SheetContent
                    side="left"
                    className="w-[90vw] sm:w-[60vw] p-0 overflow-y-auto">
                    <div className="h-full flex flex-col p-6">
                        <SheetHeader className="space-y-4">
                            <SheetTitle className="flex items-center justify-between">
                                <span>
                                    Field -{' '}
                                    <span className="font-light">
                                        {descriptionPanelHeading}
                                    </span>
                                </span>
                            </SheetTitle>
                        </SheetHeader>

                        <div className="flex-grow mt-6 overflow-y-auto">
                            <div
                                className="prose prose-sm max-w-none"
                                dangerouslySetInnerHTML={{
                                    __html: descriptionPanelContent,
                                }}
                            />
                        </div>
                    </div>
                </SheetContent>
            </Sheet>
        </>
    )
}
