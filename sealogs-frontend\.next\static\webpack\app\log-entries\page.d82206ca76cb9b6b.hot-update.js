"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, showYearPicker = false, allowFutureYears = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, ...buttonProps } = param;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Deep equality check for dates to prevent unnecessary updates\n        const isEqual = (a, b)=>{\n            if (a === b) return true;\n            // Handle Date objects\n            if (a instanceof Date && b instanceof Date) {\n                return a.getTime() === b.getTime();\n            }\n            // Handle DateRange objects\n            if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n                if (\"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n                    const fromEqual = a.from && b.from ? a.from instanceof Date && b.from instanceof Date && a.from.getTime() === b.from.getTime() : a.from === b.from;\n                    const toEqual = a.to && b.to ? a.to instanceof Date && b.to instanceof Date && a.to.getTime() === b.to.getTime() : a.to === b.to;\n                    return fromEqual && toEqual;\n                }\n            }\n            return false;\n        };\n        // Only update if the value has actually changed\n        if (!isEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const hours = value.getHours();\n                    const minutes = value.getMinutes();\n                    if (hours || minutes) setTime({\n                        hour: hours,\n                        minute: minutes\n                    });\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromHours = from.getHours();\n                    const fromMinutes = from.getMinutes();\n                    if (fromHours || fromMinutes) setTime({\n                        hour: fromHours,\n                        minute: fromMinutes\n                    });\n                    if (to instanceof Date) {\n                        const toHours = to.getHours();\n                        const toMinutes = to.getMinutes();\n                        if (toHours || toMinutes) setToTime({\n                            hour: toHours,\n                            minute: toMinutes\n                        });\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(mode === \"range\" ? {\n                    from: undefined,\n                    to: undefined\n                } : undefined);\n                // Also reset pendingSelection\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(mode === \"range\" ? {\n                from: undefined,\n                to: undefined\n            } : undefined);\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // If time is not set, initialize it with current time\n                let currentTime = time;\n                if (!currentTime && shouldIncludeTime) {\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                let currentToTime = toTime;\n                if (!currentToTime && shouldIncludeTime && to) {\n                    const now = new Date();\n                    currentToTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setToTime(currentToTime);\n                }\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // If time is not set and we should include time, preserve existing time or initialize\n                let currentTime = time;\n                // If we have an existing dateValue with time, preserve that time\n                if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                    currentTime = {\n                        hour: dateValue.getHours(),\n                        minute: dateValue.getMinutes()\n                    };\n                    setTime(currentTime);\n                } else if (!currentTime && shouldIncludeTime) {\n                    // Only use current time if no existing time is available\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n        // Close the popover after clearing\n        setOpen(false);\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Apply time if needed\n            let currentTime = time;\n            if (!currentTime && shouldIncludeTime && from) {\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            let currentToTime = toTime;\n            if (!currentToTime && shouldIncludeTime && to) {\n                const now = new Date();\n                currentToTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setToTime(currentToTime);\n            }\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Apply time if needed\n            let currentTime = time;\n            // If we have an existing dateValue with time, preserve that time\n            if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                currentTime = {\n                    hour: dateValue.getHours(),\n                    minute: dateValue.getMinutes()\n                };\n                setTime(currentTime);\n            } else if (!currentTime && shouldIncludeTime) {\n                // Only use current time if no existing time is available\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                            size: 20,\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 33\n                        }, void 0),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 624,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 685,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 694,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 701,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? icon : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                                        size: 20,\n                                        className: \"text-muted-foreground\"\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20,\n                                        className: \"text-neutral-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 37\n                                    }, void 0),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 716,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    initialFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    allowFutureYears: allowFutureYears,\n                                    showYearPicker: showYearPicker,\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from)) {\n                                                    const fromDate = pendingSelection.from;\n                                                    currentTime = {\n                                                        hour: fromDate.getHours(),\n                                                        minute: fromDate.getMinutes()\n                                                    };\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            toValue: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentToTime = toTime;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                                                    const toDate = pendingSelection.to;\n                                                    currentToTime = {\n                                                        hour: toDate.getHours(),\n                                                        minute: toDate.getMinutes()\n                                                    };\n                                                }\n                                                if (currentToTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentToTime.hour, currentToTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 766,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    initialFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    showYearPicker: showYearPicker,\n                                    allowFutureYears: allowFutureYears,\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && pendingSelection instanceof Date && !isNaN(pendingSelection.getTime())) {\n                                                    currentTime = {\n                                                        hour: pendingSelection.getHours(),\n                                                        minute: pendingSelection.getMinutes()\n                                                    };\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            toValue: toTime ? (()=>{\n                                                const date = new Date();\n                                                date.setHours(toTime.hour, toTime.minute, 0, 0);\n                                                return date;\n                                            })() : undefined,\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 764,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 705,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 699,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0RhdGVSYW5nZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFeUI7QUFDa0I7QUFDSTtBQUNJO0FBS25CO0FBQ3NDO0FBQ2Q7QUFDbkI7QUFDb0I7QUFFUjtBQUNQO0FBa0YxQyxNQUFNbUIsYUFBYTtRQUFDLEVBQ2hCQyxRQUFRLEVBQ1JDLFNBQVMsRUFDVEMsY0FBYyxhQUFhLEVBQzNCQyxPQUFPLE9BQU8sRUFDZEMsT0FBTyxNQUFNLEVBQ2JDLFdBQVcsS0FBSyxFQUNoQkMsS0FBSyxFQUNMQyxhQUFhLFlBQVksRUFDekJDLFVBQVUsRUFDVkMsaUJBQWlCTixTQUFTLFVBQVUsSUFBSSxDQUFDLEVBQ3pDTyxnQkFBZ0IsSUFBSSxFQUNwQkMsa0JBQWtCLEtBQUssRUFDdkJDLGlCQUFpQixLQUFLLEVBQ3RCQyxtQkFBbUIsS0FBSyxFQUN4QkMsY0FBYyxLQUFLLEVBQ25CQyxXQUFXLFFBQVEsRUFDbkJDLGFBQWEsT0FBTyxFQUNwQkMsZUFBZSxFQUFFLEVBQ2pCQyxLQUFLLEVBQ0xDLGdCQUFnQixLQUFLLEVBQ3JCQyxZQUFZLEtBQUssRUFDakJDLElBQUksRUFDSkMsbUJBQW1CLElBQUksRUFDdkJDLG9CQUFvQixTQUFTLEVBQzdCQyxRQUFRLEtBQUssRUFDYixHQUFHQyxhQUNXOztJQUNkLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHOUMsK0NBQVFBLENBQ3RDeUIsU0FDS0gsQ0FBQUEsU0FBUyxVQUFVO1FBQUV5QixNQUFNQztRQUFXQyxJQUFJRDtJQUFVLElBQUlBLFNBQVE7SUFHekUsdUdBQXVHO0lBQ3ZHLE1BQU0sQ0FBQ0UsTUFBTUMsUUFBUSxHQUFHbkQsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxDQUFDb0QsTUFBTUMsUUFBUSxHQUFHckQsK0NBQVFBLENBQzVCO0lBRUosTUFBTSxDQUFDc0QsUUFBUUMsVUFBVSxHQUFHdkQsK0NBQVFBLENBRzFCO0lBRVYsdUVBQXVFO0lBQ3ZFLE1BQU0sQ0FBQ3dELGtCQUFrQkMsb0JBQW9CLEdBQUd6RCwrQ0FBUUEsQ0FFdER5QixTQUFTdUI7SUFFWCxnQ0FBZ0M7SUFDaEMsTUFBTVUsb0JBQ0ZwQyxTQUFTLFVBQVUsc0JBQXNCO0lBRTdDckIsZ0RBQVNBLENBQUM7UUFDTiwrREFBK0Q7UUFDL0QsTUFBTTBELFVBQVUsQ0FBQ0MsR0FBUUM7WUFDckIsSUFBSUQsTUFBTUMsR0FBRyxPQUFPO1lBRXBCLHNCQUFzQjtZQUN0QixJQUFJRCxhQUFhRSxRQUFRRCxhQUFhQyxNQUFNO2dCQUN4QyxPQUFPRixFQUFFRyxPQUFPLE9BQU9GLEVBQUVFLE9BQU87WUFDcEM7WUFFQSwyQkFBMkI7WUFDM0IsSUFBSUgsS0FBS0MsS0FBSyxPQUFPRCxNQUFNLFlBQVksT0FBT0MsTUFBTSxVQUFVO2dCQUMxRCxJQUFJLFVBQVVELEtBQUssUUFBUUEsS0FBSyxVQUFVQyxLQUFLLFFBQVFBLEdBQUc7b0JBQ3RELE1BQU1HLFlBQ0ZKLEVBQUViLElBQUksSUFBSWMsRUFBRWQsSUFBSSxHQUNWYSxFQUFFYixJQUFJLFlBQVllLFFBQ2xCRCxFQUFFZCxJQUFJLFlBQVllLFFBQ2xCRixFQUFFYixJQUFJLENBQUNnQixPQUFPLE9BQU9GLEVBQUVkLElBQUksQ0FBQ2dCLE9BQU8sS0FDbkNILEVBQUViLElBQUksS0FBS2MsRUFBRWQsSUFBSTtvQkFFM0IsTUFBTWtCLFVBQ0ZMLEVBQUVYLEVBQUUsSUFBSVksRUFBRVosRUFBRSxHQUNOVyxFQUFFWCxFQUFFLFlBQVlhLFFBQ2hCRCxFQUFFWixFQUFFLFlBQVlhLFFBQ2hCRixFQUFFWCxFQUFFLENBQUNjLE9BQU8sT0FBT0YsRUFBRVosRUFBRSxDQUFDYyxPQUFPLEtBQy9CSCxFQUFFWCxFQUFFLEtBQUtZLEVBQUVaLEVBQUU7b0JBRXZCLE9BQU9lLGFBQWFDO2dCQUN4QjtZQUNKO1lBRUEsT0FBTztRQUNYO1FBRUEsZ0RBQWdEO1FBQ2hELElBQUksQ0FBQ04sUUFBUWxDLE9BQU9vQixZQUFZO1lBQzVCLElBQUlwQixPQUFPO2dCQUNQcUIsYUFBYXJCO2dCQUNiLDhDQUE4QztnQkFDOUNnQyxvQkFBb0JoQztnQkFFcEIsSUFBSUEsaUJBQWlCcUMsTUFBTTtvQkFDdkIsTUFBTUksUUFBUXpDLE1BQU0wQyxRQUFRO29CQUM1QixNQUFNQyxVQUFVM0MsTUFBTTRDLFVBQVU7b0JBQ2hDLElBQUlILFNBQVNFLFNBQ1RmLFFBQVE7d0JBQUVpQixNQUFNSjt3QkFBT0ssUUFBUUg7b0JBQVE7Z0JBQy9DLE9BQU8sSUFBSSxVQUFVM0MsU0FBU0EsTUFBTXNCLElBQUksWUFBWWUsTUFBTTtvQkFDdEQsTUFBTSxFQUFFZixJQUFJLEVBQUVFLEVBQUUsRUFBRSxHQUFHeEI7b0JBQ3JCLE1BQU0rQyxZQUFZekIsS0FBS29CLFFBQVE7b0JBQy9CLE1BQU1NLGNBQWMxQixLQUFLc0IsVUFBVTtvQkFDbkMsSUFBSUcsYUFBYUMsYUFDYnBCLFFBQVE7d0JBQUVpQixNQUFNRTt3QkFBV0QsUUFBUUU7b0JBQVk7b0JBQ25ELElBQUl4QixjQUFjYSxNQUFNO3dCQUNwQixNQUFNWSxVQUFVekIsR0FBR2tCLFFBQVE7d0JBQzNCLE1BQU1RLFlBQVkxQixHQUFHb0IsVUFBVTt3QkFDL0IsSUFBSUssV0FBV0MsV0FDWHBCLFVBQVU7NEJBQUVlLE1BQU1JOzRCQUFTSCxRQUFRSTt3QkFBVTtvQkFDckQ7Z0JBQ0o7WUFDSixPQUFPO2dCQUNILDJFQUEyRTtnQkFDM0U3QixhQUNJeEIsU0FBUyxVQUNIO29CQUFFeUIsTUFBTUM7b0JBQVdDLElBQUlEO2dCQUFVLElBQ2pDQTtnQkFFViw4QkFBOEI7Z0JBQzlCUyxvQkFBb0JUO1lBQ3hCO1FBQ0o7SUFDSixHQUFHO1FBQUN2QjtRQUFPSDtLQUFLO0lBRWhCLE1BQU1zRCxlQUFlLENBQUNDO1FBQ2xCLElBQUksQ0FBQ2xELFlBQVksT0FBTztRQUN4QixNQUFNLEVBQUVtRCxPQUFPLEVBQUVDLE9BQU8sRUFBRUMsYUFBYSxFQUFFQyxrQkFBa0IsRUFBRSxHQUN6RHREO1FBQ0osSUFBSW1ELFdBQVdoRSxxR0FBUUEsQ0FBQytELE1BQU1DLFVBQVUsT0FBTztRQUMvQyxJQUFJQyxXQUFXbEUscUdBQU9BLENBQUNnRSxNQUFNRSxVQUFVLE9BQU87UUFDOUMsSUFDSUMsMEJBQUFBLG9DQUFBQSxjQUFlRSxJQUFJLENBQ2YsQ0FBQ0MsZUFDR3ZFLG9HQUFNQSxDQUFDdUUsY0FBYyxrQkFDckJ2RSxvR0FBTUEsQ0FBQ2lFLE1BQU0sZ0JBR3JCLE9BQU87UUFDWCxJQUFJSSwrQkFBQUEseUNBQUFBLG1CQUFvQkcsUUFBUSxDQUFDUCxLQUFLUSxNQUFNLEtBQUssT0FBTztRQUN4RCxPQUFPO0lBQ1g7SUFFQSxNQUFNQyxZQUFZLENBQ2RULE1BQ0FVLElBRUFWLFFBQVFVLElBQ0Z4RSxpR0FBR0EsQ0FBQzhELE1BQU07WUFDTlgsT0FBT3FCLEVBQUVqQixJQUFJO1lBQ2JGLFNBQVNtQixFQUFFaEIsTUFBTTtZQUNqQmlCLFNBQVM7WUFDVEMsY0FBYztRQUNsQixLQUNBWjtJQUVWLE1BQU1hLG9CQUFvQixDQUFDQztRQUN2QixJQUFJLENBQUNBLFVBQVU7WUFDWCw2REFBNkQ7WUFDN0Q3QyxhQUNJeEIsU0FBUyxVQUNIO2dCQUFFeUIsTUFBTUM7Z0JBQVdDLElBQUlEO1lBQVUsSUFDakNBO1lBRVZTLG9CQUFvQlQ7WUFDcEI3QixTQUFTO1lBQ1Q7UUFDSjtRQUVBLElBQUlHLFNBQVMsU0FBUztZQUNsQixNQUFNLEVBQUV5QixJQUFJLEVBQUVFLEVBQUUsRUFBRSxHQUFHMEM7WUFDckIsSUFBSTVDLFFBQVEsQ0FBQzZCLGFBQWE3QixPQUFPO1lBQ2pDLElBQUlFLE1BQU0sQ0FBQzJCLGFBQWEzQixLQUFLO1lBRTdCLG9FQUFvRTtZQUNwRSxJQUFJUixrQkFBa0I7Z0JBQ2xCZ0Isb0JBQW9CO29CQUFFVjtvQkFBTUU7Z0JBQUc7WUFDbkMsT0FBTztnQkFDSEgsYUFBYTtvQkFBRUM7b0JBQU1FO2dCQUFHO2dCQUV4QixzREFBc0Q7Z0JBQ3RELElBQUkyQyxjQUFjeEM7Z0JBQ2xCLElBQUksQ0FBQ3dDLGVBQWVDLG1CQUFtQjtvQkFDbkMsTUFBTUMsTUFBTSxJQUFJaEM7b0JBQ2hCOEIsY0FBYzt3QkFDVnRCLE1BQU13QixJQUFJM0IsUUFBUTt3QkFDbEJJLFFBQVF1QixJQUFJekIsVUFBVTtvQkFDMUI7b0JBQ0FoQixRQUFRdUM7Z0JBQ1o7Z0JBRUEsSUFBSUcsZ0JBQWdCekM7Z0JBQ3BCLElBQUksQ0FBQ3lDLGlCQUFpQkYscUJBQXFCNUMsSUFBSTtvQkFDM0MsTUFBTTZDLE1BQU0sSUFBSWhDO29CQUNoQmlDLGdCQUFnQjt3QkFDWnpCLE1BQU13QixJQUFJM0IsUUFBUTt3QkFDbEJJLFFBQVF1QixJQUFJekIsVUFBVTtvQkFDMUI7b0JBQ0FkLFVBQVV3QztnQkFDZDtnQkFFQTVFLFNBQVM7b0JBQ0w2RSxXQUFXVixVQUFVdkMsTUFBTTZDO29CQUMzQkssU0FBU1gsVUFBVXJDLElBQUk4QztnQkFDM0I7WUFDSjtRQUNKLE9BQU87WUFDSCxNQUFNRyxhQUFhUDtZQUNuQixJQUFJLENBQUNmLGFBQWFzQixhQUFhO1lBRS9CLG9FQUFvRTtZQUNwRSxJQUFJekQsa0JBQWtCO2dCQUNsQmdCLG9CQUFvQnlDO1lBQ3hCLE9BQU87Z0JBQ0hwRCxhQUFhb0Q7Z0JBRWIsc0ZBQXNGO2dCQUN0RixJQUFJTixjQUFjeEM7Z0JBRWxCLGlFQUFpRTtnQkFDakUsSUFDSSxDQUFDd0MsZUFDREMscUJBQ0FoRCxxQkFBcUJpQixNQUN2QjtvQkFDRThCLGNBQWM7d0JBQ1Z0QixNQUFNekIsVUFBVXNCLFFBQVE7d0JBQ3hCSSxRQUFRMUIsVUFBVXdCLFVBQVU7b0JBQ2hDO29CQUNBaEIsUUFBUXVDO2dCQUNaLE9BQU8sSUFBSSxDQUFDQSxlQUFlQyxtQkFBbUI7b0JBQzFDLHlEQUF5RDtvQkFDekQsTUFBTUMsTUFBTSxJQUFJaEM7b0JBQ2hCOEIsY0FBYzt3QkFDVnRCLE1BQU13QixJQUFJM0IsUUFBUTt3QkFDbEJJLFFBQVF1QixJQUFJekIsVUFBVTtvQkFDMUI7b0JBQ0FoQixRQUFRdUM7Z0JBQ1o7Z0JBRUEsaURBQWlEO2dCQUNqRCxNQUFNTyxTQUFTTixvQkFDVFAsVUFBVVksWUFBWU4sZUFDdEJNO2dCQUVOL0UsU0FBU2dGO2dCQUVULElBQUl0RSxpQkFBaUIsQ0FBQ2dFLG1CQUFtQjFDLFFBQVE7WUFDckQ7UUFDSjtJQUNKO0lBRUEsTUFBTWlELG1CQUFtQixTQUFDdkI7WUFBWXdCLDRFQUFXO1FBQzdDLElBQUksQ0FBQ3hCLE1BQU07UUFFWCxNQUFNeUIsVUFBVTtZQUFFaEMsTUFBTU8sS0FBS1YsUUFBUTtZQUFJSSxRQUFRTSxLQUFLUixVQUFVO1FBQUc7UUFFbkUsK0RBQStEO1FBQy9ELElBQUlnQyxVQUFVO1lBQ1YsTUFBTU4sZ0JBQWdCekM7WUFFdEIsSUFDSSxDQUFDeUMsaUJBQ0RBLGNBQWN6QixJQUFJLEtBQUtnQyxRQUFRaEMsSUFBSSxJQUNuQ3lCLGNBQWN4QixNQUFNLEtBQUsrQixRQUFRL0IsTUFBTSxFQUN6QztnQkFDRWhCLFVBQVUrQztnQkFFViw0Q0FBNEM7Z0JBQzVDLElBQUk3RCxxQkFBcUJlLDZCQUFBQSx1Q0FBRCxpQkFBaUNQLEVBQUUsR0FBRTtvQkFDekQsTUFBTXNELGFBQWF4RixpR0FBR0EsQ0FDbEIsaUJBQWdDa0MsRUFBRSxFQUNsQzt3QkFDSWlCLE9BQU9vQyxRQUFRaEMsSUFBSTt3QkFDbkJGLFNBQVNrQyxRQUFRL0IsTUFBTTt3QkFDdkJpQixTQUFTO3dCQUNUQyxjQUFjO29CQUNsQjtvQkFHSmhDLG9CQUFvQjt3QkFDaEIsR0FBSUQsZ0JBQWdCO3dCQUNwQlAsSUFBSXNEO29CQUNSO2dCQUNKO2dCQUVBLDBEQUEwRDtnQkFDMUQsSUFBSSxDQUFDOUQsa0JBQWtCO29CQUNuQixJQUFLSSxzQkFBQUEsZ0NBQUQsVUFBMEJJLEVBQUUsRUFBRTt3QkFDOUIsTUFBTXNELGFBQWF4RixpR0FBR0EsQ0FBQyxVQUF5QmtDLEVBQUUsRUFBRzs0QkFDakRpQixPQUFPb0MsUUFBUWhDLElBQUk7NEJBQ25CRixTQUFTa0MsUUFBUS9CLE1BQU07NEJBQ3ZCaUIsU0FBUzs0QkFDVEMsY0FBYzt3QkFDbEI7d0JBRUF0RSxTQUFTOzRCQUNMNkUsV0FBVyxVQUF5QmpELElBQUk7NEJBQ3hDa0QsU0FBU007d0JBQ2I7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKLE9BQU87WUFDSCxNQUFNWCxjQUFjeEM7WUFFcEIsSUFDSSxDQUFDd0MsZUFDREEsWUFBWXRCLElBQUksS0FBS2dDLFFBQVFoQyxJQUFJLElBQ2pDc0IsWUFBWXJCLE1BQU0sS0FBSytCLFFBQVEvQixNQUFNLEVBQ3ZDO2dCQUNFbEIsUUFBUWlEO2dCQUVSLDRDQUE0QztnQkFDNUMsSUFBSTdELGtCQUFrQjtvQkFDbEIsSUFBS2UsNkJBQUFBLHVDQUFELGlCQUFpQ1QsSUFBSSxFQUFFO3dCQUN2QyxNQUFNeUQsZUFBZXpGLGlHQUFHQSxDQUNwQixpQkFBZ0NnQyxJQUFJLEVBQ3BDOzRCQUNJbUIsT0FBT29DLFFBQVFoQyxJQUFJOzRCQUNuQkYsU0FBU2tDLFFBQVEvQixNQUFNOzRCQUN2QmlCLFNBQVM7NEJBQ1RDLGNBQWM7d0JBQ2xCO3dCQUdKaEMsb0JBQW9COzRCQUNoQixHQUFJRCxnQkFBZ0I7NEJBQ3BCVCxNQUFNeUQ7d0JBQ1Y7b0JBQ0osT0FBTyxJQUFJaEQsNEJBQTRCTSxNQUFNO3dCQUN6QyxNQUFNMkMsVUFBVTFGLGlHQUFHQSxDQUFDeUMsa0JBQTBCOzRCQUMxQ1UsT0FBT29DLFFBQVFoQyxJQUFJOzRCQUNuQkYsU0FBU2tDLFFBQVEvQixNQUFNOzRCQUN2QmlCLFNBQVM7NEJBQ1RDLGNBQWM7d0JBQ2xCO3dCQUVBaEMsb0JBQW9CZ0Q7b0JBQ3hCO2dCQUNKO2dCQUVBLDBEQUEwRDtnQkFDMUQsSUFBSSxDQUFDaEUsa0JBQWtCO29CQUNuQixJQUFLSSxzQkFBQUEsZ0NBQUQsVUFBMEJFLElBQUksRUFBRTt3QkFDaEMsTUFBTXlELGVBQWV6RixpR0FBR0EsQ0FDcEIsVUFBeUJnQyxJQUFJLEVBQzdCOzRCQUNJbUIsT0FBT29DLFFBQVFoQyxJQUFJOzRCQUNuQkYsU0FBU2tDLFFBQVEvQixNQUFNOzRCQUN2QmlCLFNBQVM7NEJBQ1RDLGNBQWM7d0JBQ2xCO3dCQUdKdEUsU0FBUzs0QkFDTDZFLFdBQVdROzRCQUNYUCxTQUFTLFVBQXlCaEQsRUFBRTt3QkFDeEM7b0JBQ0osT0FBTyxJQUFJSixxQkFBcUJpQixNQUFNO3dCQUNsQyxNQUFNMkMsVUFBVTFGLGlHQUFHQSxDQUFDOEIsV0FBVzs0QkFDM0JxQixPQUFPb0MsUUFBUWhDLElBQUk7NEJBQ25CRixTQUFTa0MsUUFBUS9CLE1BQU07NEJBQ3ZCaUIsU0FBUzs0QkFDVEMsY0FBYzt3QkFDbEI7d0JBRUF0RSxTQUFTc0Y7b0JBQ2I7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFFQSxNQUFNQyxjQUFjLENBQUNDO1FBQ2pCQSxFQUFFQyxlQUFlLEdBQUcsaUNBQWlDOztRQUNyRDlELGFBQ0l4QixTQUFTLFVBQVU7WUFBRXlCLE1BQU1DO1lBQVdDLElBQUlEO1FBQVUsSUFBSUE7UUFFNURTLG9CQUFvQlQ7UUFDcEJLLFFBQVE7UUFDUkUsVUFBVTtRQUNWcEMsU0FBUztJQUNiO0lBRUEsMkRBQTJEO0lBQzNELE1BQU0wRix1QkFBdUI7UUFDekIvRCxhQUNJeEIsU0FBUyxVQUFVO1lBQUV5QixNQUFNQztZQUFXQyxJQUFJRDtRQUFVLElBQUlBO1FBRTVEUyxvQkFBb0JUO1FBQ3BCSyxRQUFRO1FBQ1JFLFVBQVU7UUFDVnBDLFNBQVM7UUFDVCxtQ0FBbUM7UUFDbkNnQyxRQUFRO0lBQ1o7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTTJELGdCQUFnQjtRQUNsQixJQUFJLENBQUN0RCxrQkFBa0I7UUFFdkIsSUFBSWxDLFNBQVMsU0FBUztZQUNsQixNQUFNLEVBQUV5QixJQUFJLEVBQUVFLEVBQUUsRUFBRSxHQUFHTztZQUNyQlYsYUFBYTtnQkFBRUM7Z0JBQU1FO1lBQUc7WUFFeEIsdUJBQXVCO1lBQ3ZCLElBQUkyQyxjQUFjeEM7WUFDbEIsSUFBSSxDQUFDd0MsZUFBZUMscUJBQXFCOUMsTUFBTTtnQkFDM0MsTUFBTStDLE1BQU0sSUFBSWhDO2dCQUNoQjhCLGNBQWM7b0JBQUV0QixNQUFNd0IsSUFBSTNCLFFBQVE7b0JBQUlJLFFBQVF1QixJQUFJekIsVUFBVTtnQkFBRztnQkFDL0RoQixRQUFRdUM7WUFDWjtZQUVBLElBQUlHLGdCQUFnQnpDO1lBQ3BCLElBQUksQ0FBQ3lDLGlCQUFpQkYscUJBQXFCNUMsSUFBSTtnQkFDM0MsTUFBTTZDLE1BQU0sSUFBSWhDO2dCQUNoQmlDLGdCQUFnQjtvQkFDWnpCLE1BQU13QixJQUFJM0IsUUFBUTtvQkFDbEJJLFFBQVF1QixJQUFJekIsVUFBVTtnQkFDMUI7Z0JBQ0FkLFVBQVV3QztZQUNkO1lBRUE1RSxTQUFTO2dCQUNMNkUsV0FBV1YsVUFBVXZDLE1BQU02QztnQkFDM0JLLFNBQVNYLFVBQVVyQyxJQUFJOEM7WUFDM0I7UUFDSixPQUFPO1lBQ0gsTUFBTUcsYUFBYTFDO1lBQ25CVixhQUFhb0Q7WUFFYix1QkFBdUI7WUFDdkIsSUFBSU4sY0FBY3hDO1lBRWxCLGlFQUFpRTtZQUNqRSxJQUNJLENBQUN3QyxlQUNEQyxxQkFDQWhELHFCQUFxQmlCLE1BQ3ZCO2dCQUNFOEIsY0FBYztvQkFDVnRCLE1BQU16QixVQUFVc0IsUUFBUTtvQkFDeEJJLFFBQVExQixVQUFVd0IsVUFBVTtnQkFDaEM7Z0JBQ0FoQixRQUFRdUM7WUFDWixPQUFPLElBQUksQ0FBQ0EsZUFBZUMsbUJBQW1CO2dCQUMxQyx5REFBeUQ7Z0JBQ3pELE1BQU1DLE1BQU0sSUFBSWhDO2dCQUNoQjhCLGNBQWM7b0JBQUV0QixNQUFNd0IsSUFBSTNCLFFBQVE7b0JBQUlJLFFBQVF1QixJQUFJekIsVUFBVTtnQkFBRztnQkFDL0RoQixRQUFRdUM7WUFDWjtZQUVBLE1BQU1PLFNBQVNOLG9CQUNUUCxVQUFVWSxZQUFZTixlQUN0Qk07WUFFTi9FLFNBQVNnRjtRQUNiO1FBRUEsdUNBQXVDO1FBQ3ZDaEQsUUFBUTtRQUNSLDBCQUEwQjtRQUMxQk0sb0JBQW9CVDtJQUN4QjtJQUVBLDhEQUE4RDtJQUU5RCxnR0FBZ0c7SUFDaEcsTUFBTSxDQUFDNkMsbUJBQW1Ca0IscUJBQXFCLEdBQUcvRywrQ0FBUUEsQ0FDdER1QixTQUFTLGNBQWNVO0lBRzNCLDREQUE0RDtJQUM1RGhDLGdEQUFTQSxDQUFDO1FBQ044RyxxQkFBcUJ4RixTQUFTLGNBQWNVO0lBQ2hELEdBQUc7UUFBQ1Y7UUFBTVU7S0FBWTtJQUV0QixNQUFNK0Usb0JBQW9CbkIsb0JBQ3BCLEdBQWlCMUQsT0FBZFQsWUFBVyxLQUFjLE9BQVhTLGNBQ2pCVDtJQUVOLDhCQUE4QjtJQUM5QixNQUFNdUYscUJBQXFCLENBQUNwQztRQUN4QixJQUFJLENBQUNBLE1BQU0sT0FBTztRQUNsQixNQUFNcUMsWUFBWXJDLGdCQUFnQmYsT0FBT2UsT0FBTyxJQUFJZjtRQUNwRCxPQUFPcUQsTUFBTUQsVUFBVW5ELE9BQU8sTUFDeEIsS0FDQW5ELG9HQUFNQSxDQUFDc0csV0FBV0Y7SUFDNUI7SUFFQSxnRkFBZ0Y7SUFFaEYsSUFBSXhGLFVBQVU7UUFDVixrRUFBa0U7UUFDbEUsTUFBTTRGLGNBQWMsQ0FBQ3ZDO1lBQ2pCLElBQUksQ0FBQ0EsUUFBUSxDQUFFQSxDQUFBQSxnQkFBZ0JmLElBQUcsS0FBTXFELE1BQU10QyxLQUFLZCxPQUFPLEtBQUs7Z0JBQzNELE9BQU9uRCxvR0FBTUEsQ0FBQyxJQUFJa0QsUUFBUXBDO1lBQzlCO1lBQ0EsT0FBT2Qsb0dBQU1BLENBQ1RpRSxNQUNBZ0Isb0JBQW9CbUIsb0JBQW9CdEY7UUFFaEQ7UUFFQSw4Q0FBOEM7UUFDOUMsSUFBSTJGO1FBQ0osSUFBSS9GLFNBQVMsU0FBUztZQUNsQixNQUFNZ0csUUFBUXpFO1lBQ2QsSUFBSXlFLGtCQUFBQSw0QkFBQUEsTUFBT3ZFLElBQUksRUFBRTtnQkFDYnNFLGVBQWVDLE1BQU1yRSxFQUFFLEdBQ2pCLEdBQWdDbUUsT0FBN0JBLFlBQVlFLE1BQU12RSxJQUFJLEdBQUUsT0FBMkIsT0FBdEJxRSxZQUFZRSxNQUFNckUsRUFBRSxLQUNwRG1FLFlBQVlFLE1BQU12RSxJQUFJO1lBQ2hDLE9BQU87Z0JBQ0gsTUFBTXdFLG1CQUFtQjNHLG9HQUFNQSxDQUFDLElBQUlrRCxRQUFRcEM7Z0JBQzVDMkYsZUFBZSxHQUF5QkUsT0FBdEJBLGtCQUFpQixPQUFzQixPQUFqQkE7WUFDNUM7UUFDSixPQUFPO1lBQ0hGLGVBQ0l4RSxxQkFBcUJpQixPQUNmc0QsWUFBWXZFLGFBQ1pqQyxvR0FBTUEsQ0FBQyxJQUFJa0QsUUFBUXBDO1FBQ2pDO1FBRUEscUJBQ0ksOERBQUM4Rjs7Z0JBQ0luRix1QkFDRyw4REFBQ3JCLDRDQUFLQTtvQkFDRnlHLE9BQU87b0JBQ1BDLElBQUc7b0JBQ0hDLFVBQVVyRjtvQkFDVmQsVUFBVUE7OEJBQ1RhOzs7Ozs7OEJBR1QsOERBQUNtRjtvQkFBSXBHLFdBQVU7OEJBQ1gsNEVBQUNsQix5REFBTUE7d0JBQ0h3SCxJQUFHO3dCQUNIRSxTQUFRO3dCQUNScEcsVUFBVUE7d0JBQ1ZKLFdBQVdULGtEQUFFQSxDQUFDO3dCQUNka0gsVUFDSXJGLHFCQUNJekMsMkRBQW9CLENBQUN5QyxzQkFDakJ6Qyx5REFBa0IsQ0FDZHlDLE1BQ0E7NEJBQ0lwQixXQUNJO3dCQUNSLG1CQUdKckIsMERBQW1CLENBQUN5QyxNQUFhOzRCQUM3QnlGLE1BQU07NEJBQ043RyxXQUNJO3dCQUNSLG1CQUdKLDhEQUFDYixpR0FBWUE7NEJBQUNhLFdBQVU7Ozs7Ozt3QkFHL0IsR0FBR3dCLFdBQVc7d0JBQ2ZyQixNQUFLO2tDQUNKOEY7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3JCO0lBRUEsTUFBTWEsb0JBQW9CO1FBQ3RCLElBQUk1RyxTQUFTLFNBQVM7WUFDbEIsTUFBTWdHLFFBQVF6RTtZQUNkLElBQUl5RSxrQkFBQUEsNEJBQUFBLE1BQU92RSxJQUFJLEVBQUU7Z0JBQ2IsT0FBT3VFLE1BQU1yRSxFQUFFLGlCQUNYOzt3QkFDS2dFLG1CQUFtQkssTUFBTXZFLElBQUk7d0JBQUU7d0JBQUc7d0JBQ2xDa0UsbUJBQW1CSyxNQUFNckUsRUFBRTs7bUNBR2hDZ0UsbUJBQW1CSyxNQUFNdkUsSUFBSTtZQUVyQztZQUNBLDZDQUE2QztZQUM3QyxxQkFDSSw4REFBQ29GO2dCQUFLL0csV0FBVTswQkFDWHNDOzs7Ozs7UUFHYixPQUFPLElBQUliLFdBQVc7WUFDbEIsT0FBT29FLG1CQUFtQnBFO1FBQzlCO1FBQ0EsNkNBQTZDO1FBQzdDLHFCQUNJLDhEQUFDc0Y7WUFBSy9HLFdBQVU7c0JBQXlCc0M7Ozs7OztJQUVqRDtJQUVBLHFCQUNJLDhEQUFDOEQ7O1lBQ0luRix1QkFDRyw4REFBQ3JCLDRDQUFLQTtnQkFBQzBHLElBQUc7Z0JBQU9DLFVBQVVyRjtnQkFBZWQsVUFBVUE7MEJBQy9DYTs7Ozs7OzBCQUdULDhEQUFDakMsMkRBQU9BO2dCQUNKdUMsT0FBT0E7Z0JBQ1BPLE1BQU1BO2dCQUNOa0YsY0FBYyxDQUFDQztvQkFDWCwrRUFBK0U7b0JBQy9FLElBQUlBLFVBQVUsQ0FBQzdFLG9CQUFvQlgsV0FBVzt3QkFDMUNZLG9CQUFvQlo7b0JBQ3hCO29CQUNBTSxRQUFRa0Y7Z0JBQ1o7O2tDQUNBLDhEQUFDL0gsa0VBQWNBO3dCQUFDbUgsT0FBTztrQ0FDbkIsNEVBQUNEOzRCQUFJcEcsV0FBVTs7OENBQ1gsOERBQUNsQix5REFBTUE7b0NBQ0h3SCxJQUFHO29DQUNIRSxTQUFRO29DQUNScEcsVUFBVUE7b0NBQ1ZxRyxVQUNJckYscUJBQ0l6QywyREFBb0IsQ0FBQ3lDLFFBQ2pCQSxxQkFFQXpDLDBEQUFtQixDQUFDeUMsTUFBYTt3Q0FDN0J5RixNQUFNO3dDQUNON0csV0FBVztvQ0FDZixtQkFHSiw4REFBQ2IsaUdBQVlBO3dDQUNUMEgsTUFBTTt3Q0FDTjdHLFdBQVU7Ozs7OztvQ0FJdEJBLFdBQVdULGtEQUFFQSxDQUNULDZCQUNBNEIsYUFBYTtvQ0FFaEIsR0FBR0ssV0FBVztvQ0FDZnJCLE1BQUs7OENBQ0oyRzs7Ozs7O2dDQUVKM0YsYUFDSU0sQ0FBQUEscUJBQXFCaUIsU0FDakJqQixzQkFBQUEsZ0NBQUQsVUFBMEJFLElBQUksQ0FBRCxtQkFDN0IsOERBQUM3Qyx5REFBTUE7b0NBQ0hxQixNQUFLO29DQUNMcUcsU0FBUTtvQ0FDUkssTUFBSztvQ0FDTDdHLFdBQVU7b0NBQ1ZrSCxTQUFTNUI7b0NBQ1Q2QixjQUFXOzhDQUNYLDRFQUFDL0gsaUdBQUNBO3dDQUNFeUgsTUFBTTt3Q0FDTjdHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTWxDLDhEQUFDZixrRUFBY0E7d0JBQUNlLFdBQVU7a0NBQ3JCRSxTQUFTLHdCQUNOLDhEQUFDa0c7OzhDQUNHLDhEQUFDckgsNkRBQVFBO29DQUVMcUksWUFBWTtvQ0FDWmxILE1BQUs7b0NBQ0xtSCxPQUNJLENBQUNoRyxvQkFBb0JlLG1CQUNkQSw2QkFBQUEsdUNBQUQsaUJBQWlDVCxJQUFJLEdBQ3BDRixzQkFBQUEsZ0NBQUQsVUFBMEJFLElBQUksS0FDcEMsSUFBSWU7b0NBRVI5QixrQkFBa0JBO29DQUNsQkQsZ0JBQWdCQTtvQ0FDaEIyRyxVQUNJakcsb0JBQW9CZSxtQkFDYkEsbUJBQ0FYO29DQUVYOEYsVUFBVSxDQUFDbEg7d0NBQ1AsK0RBQStEO3dDQUMvRGlFLGtCQUFrQmpFO29DQUN0QjtvQ0FDQUcsZ0JBQWdCQTtvQ0FDaEJnSCxnQkFBZ0I5RztvQ0FDaEJOLFVBQ0lHLGFBQ00sQ0FBQ2tELE9BQVMsQ0FBQ0QsYUFBYUMsUUFDeEI3QjttQ0F6Qk47Ozs7O2dDQTRCUDZDLG1DQUNHLDhEQUFDMkI7b0NBQUlwRyxXQUFVOzhDQUNYLDRFQUFDb0c7d0NBQUlwRyxXQUFVO2tEQUNYLDRFQUFDVixrRUFBVUE7NENBQ1BlLE9BQU8sQ0FBQztnREFDSiw0RUFBNEU7Z0RBQzVFLElBQUltRSxjQUFjeEM7Z0RBQ2xCLElBQ0lYLHFCQUVJZSw2QkFBQUEsdUNBREosaUJBRUdULElBQUksR0FDVDtvREFDRSxNQUFNOEYsV0FBVyxpQkFFZjlGLElBQUk7b0RBQ042QyxjQUFjO3dEQUNWdEIsTUFBTXVFLFNBQVMxRSxRQUFRO3dEQUN2QkksUUFBUXNFLFNBQVN4RSxVQUFVO29EQUMvQjtnREFDSjtnREFFQSxJQUFJdUIsYUFBYTtvREFDYixNQUFNZixPQUFPLElBQUlmO29EQUNqQmUsS0FBS2lFLFFBQVEsQ0FDVGxELFlBQVl0QixJQUFJLEVBQ2hCc0IsWUFBWXJCLE1BQU0sRUFDbEIsR0FDQTtvREFFSixPQUFPTTtnREFDWDtnREFDQSxPQUFPN0I7NENBQ1g7NENBQ0ErRixTQUFTLENBQUM7Z0RBQ04sNEVBQTRFO2dEQUM1RSxJQUFJaEQsZ0JBQWdCekM7Z0RBQ3BCLElBQ0liLHFCQUVJZSw2QkFBQUEsdUNBREosaUJBRUdQLEVBQUUsR0FDUDtvREFDRSxNQUFNK0YsU0FBUyxpQkFFYi9GLEVBQUU7b0RBQ0o4QyxnQkFBZ0I7d0RBQ1p6QixNQUFNMEUsT0FBTzdFLFFBQVE7d0RBQ3JCSSxRQUFReUUsT0FBTzNFLFVBQVU7b0RBQzdCO2dEQUNKO2dEQUVBLElBQUkwQixlQUFlO29EQUNmLE1BQU1sQixPQUFPLElBQUlmO29EQUNqQmUsS0FBS2lFLFFBQVEsQ0FDVC9DLGNBQWN6QixJQUFJLEVBQ2xCeUIsY0FBY3hCLE1BQU0sRUFDcEIsR0FDQTtvREFFSixPQUFPTTtnREFDWDtnREFDQSxPQUFPN0I7NENBQ1g7NENBQ0E3QixVQUFVaUY7NENBQ1Y2QyxZQUFZLENBQUNwRSxPQUNUdUIsaUJBQWlCdkIsTUFBTTs0Q0FFM0IscUVBQXFFOzRDQUNyRXJELFVBQVU7NENBQ1ZKLFdBQVU7NENBQ1ZFLE1BQU1ZOzRDQUNORyxPQUNJSCxhQUFhLFVBQ1AsZUFDQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBTXpCUCxrQ0FDRzs7c0RBQ0ksOERBQUN4QixvREFBU0E7NENBQUNHLFdBQVU7Ozs7OztzREFDckIsOERBQUNvRzs0Q0FDR3BHLFdBQVdULGtEQUFFQSxDQUNUOztnREFFSDRCLDJCQUNHLDhEQUFDaUY7b0RBQUlwRyxXQUFVOzhEQUNYLDRFQUFDbEIseURBQU1BO3dEQUNIMEgsU0FBUTt3REFDUlUsU0FDSXpCO3dEQUVKZ0IsVUFBVXJILGlHQUFDQTt3REFDWFksV0FBVTt3REFDVm1ILGNBQVc7a0VBQW1COzs7Ozs7Ozs7Ozs4REFLMUMsOERBQUNySSx5REFBTUE7b0RBQ0hvSSxTQUFTeEI7b0RBQ1R0RixVQUFVLENBQUNnQztvREFDWHFFLFVBQVVwSCxpR0FBS0E7OERBQ2RpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFPckIsOERBQUM4RTs7OENBQ0csOERBQUNySCw2REFBUUE7b0NBRUxxSSxZQUFZO29DQUNabEgsTUFBSztvQ0FDTG1ILE9BQ0ksQ0FBQ2hHLG9CQUFvQmUsbUJBQ2RBLG1CQUNBWCxTQUFpQixLQUFNLElBQUlpQjtvQ0FFdEMvQixnQkFBZ0JBO29DQUNoQkMsa0JBQWtCQTtvQ0FDbEIwRyxVQUNJakcsb0JBQW9CZSxtQkFDYkEsbUJBQ0FYO29DQUVYOEYsVUFBVSxDQUFDbEg7d0NBQ1AsK0RBQStEO3dDQUMvRGlFLGtCQUFrQmpFO29DQUN0QjtvQ0FDQUcsZ0JBQWdCQTtvQ0FDaEJnSCxnQkFBZ0I5RztvQ0FDaEJOLFVBQ0lHLGFBQ00sQ0FBQ2tELE9BQVMsQ0FBQ0QsYUFBYUMsUUFDeEI3QjttQ0F4Qk47Ozs7O2dDQTJCUDZDLG1DQUNHLDhEQUFDMkI7b0NBQUlwRyxXQUFVOzhDQUNYLDRFQUFDb0c7d0NBQUlwRyxXQUFVO2tEQUNYLDRFQUFDVixrRUFBVUE7NENBQ1BlLE9BQU8sQ0FBQztnREFDSiw0RUFBNEU7Z0RBQzVFLElBQUltRSxjQUFjeEM7Z0RBRWxCLElBQ0lYLG9CQUNBZSw0QkFDSU0sUUFDSixDQUFDcUQsTUFDRzNELGlCQUFpQk8sT0FBTyxLQUU5QjtvREFDRTZCLGNBQWM7d0RBQ1Z0QixNQUFNZCxpQkFBaUJXLFFBQVE7d0RBQy9CSSxRQUFRZixpQkFBaUJhLFVBQVU7b0RBQ3ZDO2dEQUNKO2dEQUVBLElBQUl1QixhQUFhO29EQUNiLE1BQU1mLE9BQU8sSUFBSWY7b0RBQ2pCZSxLQUFLaUUsUUFBUSxDQUNUbEQsWUFBWXRCLElBQUksRUFDaEJzQixZQUFZckIsTUFBTSxFQUNsQixHQUNBO29EQUVKLE9BQU9NO2dEQUNYO2dEQUNBLE9BQU83Qjs0Q0FDWDs0Q0FDQStGLFNBQ0l6RixTQUNNLENBQUM7Z0RBQ0csTUFBTXVCLE9BQ0YsSUFBSWY7Z0RBQ1JlLEtBQUtpRSxRQUFRLENBQ1R4RixPQUFPZ0IsSUFBSSxFQUNYaEIsT0FBT2lCLE1BQU0sRUFDYixHQUNBO2dEQUVKLE9BQU9NOzRDQUNYLE9BQ0E3Qjs0Q0FFVjdCLFVBQVVpRjs0Q0FDVjZDLFlBQVksQ0FBQ3BFLE9BQ1R1QixpQkFBaUJ2QixNQUFNOzRDQUUzQixxRUFBcUU7NENBQ3JFckQsVUFBVTs0Q0FDVkosV0FBVTs0Q0FDVkUsTUFBTVk7NENBQ05HLE9BQ0lILGFBQWEsVUFDUCxlQUNBYzs7Ozs7Ozs7Ozs7Ozs7OztnQ0FNekJQLGtDQUNHOztzREFDSSw4REFBQ3hCLG9EQUFTQTs0Q0FBQ0csV0FBVTs7Ozs7O3NEQUNyQiw4REFBQ29HOzRDQUNHcEcsV0FBV1Qsa0RBQUVBLENBQ1Q7O2dEQUVINEIsMkJBQ0csOERBQUNpRjtvREFBSXBHLFdBQVU7OERBQ1gsNEVBQUNsQix5REFBTUE7d0RBQ0gwSCxTQUFRO3dEQUNSVSxTQUNJekI7d0RBRUpnQixVQUFVckgsaUdBQUNBO3dEQUNYK0gsY0FBVztrRUFBYTs7Ozs7Ozs7Ozs7OERBS3BDLDhEQUFDckkseURBQU1BO29EQUNIb0ksU0FBU3hCO29EQUNUdEYsVUFBVSxDQUFDZ0M7b0RBQ1hwQyxXQUFXVCxrREFBRUEsQ0FDVDRCLFlBQVksS0FBSzs4REFFcEJHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVzdDO0dBNzZCTXhCO0tBQUFBO0FBKzZCTiwrREFBZUEsVUFBVUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9EYXRlUmFuZ2UudHN4PzllNjQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcclxuaW1wb3J0IHsgQ2FsZW5kYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FsZW5kYXInXHJcbmltcG9ydCB7XHJcbiAgICBQb3BvdmVyLFxyXG4gICAgUG9wb3ZlckNvbnRlbnQsXHJcbiAgICBQb3BvdmVyVHJpZ2dlcixcclxufSBmcm9tICdAL2NvbXBvbmVudHMvdWkvcG9wb3ZlcidcclxuaW1wb3J0IHsgQ2FsZW5kYXJJY29uLCBYLCBDaGVjaywgdHlwZSBMdWNpZGVJY29uIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5pbXBvcnQgeyBUaW1lUGlja2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RpbWUtcGlja2VyJ1xyXG5pbXBvcnQgeyBjbiB9IGZyb20gJy4uL2FwcC9saWIvdXRpbHMnXHJcbmltcG9ydCB7IGZvcm1hdCwgaXNBZnRlciwgaXNCZWZvcmUsIHNldCB9IGZyb20gJ2RhdGUtZm5zJ1xyXG5pbXBvcnQgdHlwZSB7IERhdGVSYW5nZSB9IGZyb20gJ3JlYWN0LWRheS1waWNrZXInXHJcbmltcG9ydCB7IExhYmVsLCBMYWJlbFBvc2l0aW9uIH0gZnJvbSAnLi91aS9sYWJlbCdcclxuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSAnLi91aS9zZXBhcmF0b3InXHJcbi8vIFNlbGVjdCBjb21wb25lbnRzIHJlbW92ZWQgaW4gZmF2b3Igb2YgVGltZVBpY2tlclxyXG5cclxudHlwZSBEYXRlUGlja2VyTW9kZSA9ICdyYW5nZScgfCAnc2luZ2xlJ1xyXG50eXBlIFRpbWVQaWNrZXJNb2RlID0gJ3NpbmdsZScgfCAncmFuZ2UnXHJcbnR5cGUgRGF0ZVBpY2tlclR5cGUgPSAnZGF0ZScgfCAnZGF0ZXRpbWUnXHJcblxyXG4vLyBEZWZpbmUgaWNvbiB0eXBlIHRoYXQgY2FuIGJlIGVpdGhlciBhIFJlYWN0IG5vZGUgb3IgYSBMdWNpZGUgaWNvbiBjb21wb25lbnRcclxudHlwZSBJY29uVHlwZSA9IFJlYWN0LlJlYWN0Tm9kZSB8IEx1Y2lkZUljb25cclxuXHJcbmludGVyZmFjZSBEYXRlVmFsaWRhdGlvbiB7XHJcbiAgICBtaW5EYXRlPzogRGF0ZVxyXG4gICAgbWF4RGF0ZT86IERhdGVcclxuICAgIGRpc2FibGVkRGF0ZXM/OiBEYXRlW11cclxuICAgIGRpc2FibGVkRGF5c09mV2Vlaz86IG51bWJlcltdIC8vIDAgPSBTdW5kYXksIDYgPSBTYXR1cmRheVxyXG59XHJcblxyXG5pbnRlcmZhY2UgRGF0ZVBpY2tlclByb3BzXHJcbiAgICBleHRlbmRzIE9taXQ8XHJcbiAgICAgICAgUmVhY3QuQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+LFxyXG4gICAgICAgICd2YWx1ZScgfCAnb25DaGFuZ2UnIHwgJ3R5cGUnXHJcbiAgICA+IHtcclxuICAgIG9uQ2hhbmdlOiAodmFsdWU6IGFueSkgPT4gdm9pZFxyXG4gICAgY2xhc3NOYW1lPzogc3RyaW5nXHJcbiAgICBwbGFjZWhvbGRlcj86IHN0cmluZ1xyXG4gICAgLyoqXHJcbiAgICAgKiBNb2RlIG9mIHRoZSBkYXRlIHBpY2tlci5cclxuICAgICAqICdyYW5nZScgZm9yIGRhdGUgcmFuZ2Ugc2VsZWN0aW9uIChkZWZhdWx0KVxyXG4gICAgICogJ3NpbmdsZScgZm9yIHNpbmdsZSBkYXRlIHNlbGVjdGlvblxyXG4gICAgICovXHJcbiAgICBtb2RlPzogRGF0ZVBpY2tlck1vZGVcclxuICAgIC8qKlxyXG4gICAgICogVHlwZSBvZiB0aGUgZGF0ZSBwaWNrZXIuXHJcbiAgICAgKiAnZGF0ZScgZm9yIGRhdGUgb25seSBzZWxlY3Rpb24gKGRlZmF1bHQpXHJcbiAgICAgKiAnZGF0ZXRpbWUnIGZvciBkYXRlIGFuZCB0aW1lIHNlbGVjdGlvblxyXG4gICAgICovXHJcbiAgICB0eXBlPzogRGF0ZVBpY2tlclR5cGVcclxuICAgIGRpc2FibGVkPzogYm9vbGVhblxyXG4gICAgdmFsdWU/OiBEYXRlIHwgRGF0ZVJhbmdlXHJcbiAgICAvKipcclxuICAgICAqIERhdGUgZm9ybWF0IGZvciBkaXNwbGF5XHJcbiAgICAgKiBAZGVmYXVsdCAnTExMIGRkLCB5J1xyXG4gICAgICovXHJcbiAgICBkYXRlRm9ybWF0Pzogc3RyaW5nXHJcbiAgICAvKiogVmFsaWRhdGlvbiBydWxlcyBmb3IgZGF0ZSBzZWxlY3Rpb24gKi9cclxuICAgIHZhbGlkYXRpb24/OiBEYXRlVmFsaWRhdGlvblxyXG4gICAgLyoqIE51bWJlciBvZiBtb250aHMgdG8gZGlzcGxheSBpbiB0aGUgY2FsZW5kYXIgKi9cclxuICAgIG51bWJlck9mTW9udGhzPzogbnVtYmVyXHJcbiAgICAvKiogV2hldGhlciB0byBjbG9zZSB0aGUgcG9wb3ZlciB3aGVuIGEgZGF0ZSBpcyBzZWxlY3RlZCAoc2luZ2xlIG1vZGUgb25seSkgKi9cclxuICAgIGNsb3NlT25TZWxlY3Q/OiBib29sZWFuXHJcbiAgICAvKiogV2hldGhlciB0byBzaG93IHdlZWsgbnVtYmVycyAqL1xyXG4gICAgc2hvd1dlZWtOdW1iZXJzPzogYm9vbGVhblxyXG4gICAgYWxsb3dGdXR1cmVZZWFycz86IGJvb2xlYW5cclxuXHJcbiAgICBzaG93WWVhclBpY2tlcj86IGJvb2xlYW5cclxuICAgIC8qKiBXaGV0aGVyIHRvIGluY2x1ZGUgdGltZSBwaWNrZXIgKGRlcHJlY2F0ZWQsIHVzZSB0eXBlPSdkYXRldGltZScgaW5zdGVhZCkgKi9cclxuICAgIGluY2x1ZGVUaW1lPzogYm9vbGVhblxyXG4gICAgLyoqIE1vZGUgb2YgdGhlIHRpbWUgcGlja2VyOiAnc2luZ2xlJyBvciAncmFuZ2UnICovXHJcbiAgICB0aW1lTW9kZT86IFRpbWVQaWNrZXJNb2RlXHJcbiAgICAvKiogVGltZSBmb3JtYXQgZm9yIGRpc3BsYXkgKi9cclxuICAgIHRpbWVGb3JtYXQ/OiBzdHJpbmdcclxuICAgIC8qKiBJbnRlcnZhbCBiZXR3ZWVuIHRpbWUgb3B0aW9ucyBpbiBtaW51dGVzICovXHJcbiAgICB0aW1lSW50ZXJ2YWw/OiBudW1iZXJcclxuICAgIC8qKiBPcHRpb25hbCBsYWJlbCBmb3IgdGhlIGRhdGUgcGlja2VyICovXHJcbiAgICBsYWJlbD86IHN0cmluZ1xyXG4gICAgLyoqIFBvc2l0aW9uIG9mIHRoZSBsYWJlbCByZWxhdGl2ZSB0byB0aGUgZGF0ZSBwaWNrZXIgKi9cclxuICAgIGxhYmVsUG9zaXRpb24/OiBMYWJlbFBvc2l0aW9uXHJcbiAgICAvKiogV2hldGhlciB0byBzaG93IGEgY2xlYXIgYnV0dG9uIGluc2lkZSB0aGUgZGF0ZSBwaWNrZXIgKi9cclxuICAgIGNsZWFyYWJsZT86IGJvb2xlYW5cclxuICAgIC8qKlxyXG4gICAgICogQ3VzdG9tIGljb24gdG8gZGlzcGxheSBpbiB0aGUgZGF0ZSBwaWNrZXIgYnV0dG9uLlxyXG4gICAgICogQ2FuIGJlIGEgTHVjaWRlIGljb24gY29tcG9uZW50IChDYWxlbmRhckljb24pIG9yIGEgSlNYIGVsZW1lbnQgKDxDYWxlbmRhckljb24gLz4pXHJcbiAgICAgKi9cclxuICAgIGljb24/OiBJY29uVHlwZVxyXG4gICAgLyoqIFdoZXRoZXIgdG8gc2hvdyBhIGNvbmZpcm1hdGlvbiBidXR0b24gYWZ0ZXIgc2VsZWN0aW5nIGEgZGF0ZS90aW1lICovXHJcbiAgICBjb25maXJtU2VsZWN0aW9uPzogYm9vbGVhblxyXG4gICAgLyoqIFRleHQgdG8gZGlzcGxheSBvbiB0aGUgY29uZmlybWF0aW9uIGJ1dHRvbiAqL1xyXG4gICAgY29uZmlybUJ1dHRvblRleHQ/OiBzdHJpbmdcclxuICAgIC8qKiBXaGV0aGVyIHRvIHVzZSBhIG1vZGFsIGZvciB0aGUgZGF0ZSBwaWNrZXIgKi9cclxuICAgIG1vZGFsPzogYm9vbGVhblxyXG59XHJcblxyXG5jb25zdCBEYXRlUGlja2VyID0gKHtcclxuICAgIG9uQ2hhbmdlLFxyXG4gICAgY2xhc3NOYW1lLFxyXG4gICAgcGxhY2Vob2xkZXIgPSAnU2VsZWN0IGRhdGUnLFxyXG4gICAgbW9kZSA9ICdyYW5nZScsXHJcbiAgICB0eXBlID0gJ2RhdGUnLFxyXG4gICAgZGlzYWJsZWQgPSBmYWxzZSxcclxuICAgIHZhbHVlLFxyXG4gICAgZGF0ZUZvcm1hdCA9ICdkZCBMTExMLCB5JyxcclxuICAgIHZhbGlkYXRpb24sXHJcbiAgICBudW1iZXJPZk1vbnRocyA9IG1vZGUgPT09ICdyYW5nZScgPyAyIDogMSxcclxuICAgIGNsb3NlT25TZWxlY3QgPSB0cnVlLFxyXG4gICAgc2hvd1dlZWtOdW1iZXJzID0gZmFsc2UsXHJcbiAgICBzaG93WWVhclBpY2tlciA9IGZhbHNlLFxyXG4gICAgYWxsb3dGdXR1cmVZZWFycyA9IGZhbHNlLFxyXG4gICAgaW5jbHVkZVRpbWUgPSBmYWxzZSwgLy8gZGVwcmVjYXRlZCwgdXNlIHR5cGU9J2RhdGV0aW1lJyBpbnN0ZWFkXHJcbiAgICB0aW1lTW9kZSA9ICdzaW5nbGUnLFxyXG4gICAgdGltZUZvcm1hdCA9ICdISDptbScsXHJcbiAgICB0aW1lSW50ZXJ2YWwgPSAzMCxcclxuICAgIGxhYmVsLFxyXG4gICAgbGFiZWxQb3NpdGlvbiA9ICd0b3AnLFxyXG4gICAgY2xlYXJhYmxlID0gZmFsc2UsXHJcbiAgICBpY29uLFxyXG4gICAgY29uZmlybVNlbGVjdGlvbiA9IHRydWUsXHJcbiAgICBjb25maXJtQnV0dG9uVGV4dCA9ICdDb25maXJtJyxcclxuICAgIG1vZGFsID0gZmFsc2UsXHJcbiAgICAuLi5idXR0b25Qcm9wc1xyXG59OiBEYXRlUGlja2VyUHJvcHMpID0+IHtcclxuICAgIGNvbnN0IFtkYXRlVmFsdWUsIHNldERhdGVWYWx1ZV0gPSB1c2VTdGF0ZTxEYXRlUmFuZ2UgfCBEYXRlIHwgdW5kZWZpbmVkPihcclxuICAgICAgICB2YWx1ZSB8fFxyXG4gICAgICAgICAgICAobW9kZSA9PT0gJ3JhbmdlJyA/IHsgZnJvbTogdW5kZWZpbmVkLCB0bzogdW5kZWZpbmVkIH0gOiB1bmRlZmluZWQpLFxyXG4gICAgKVxyXG5cclxuICAgIC8vIFdlJ2xsIHVzZSBidXR0b25Qcm9wcyBkaXJlY3RseSBidXQgZW5zdXJlIHdlIGRvbid0IHBhc3Mgb3VyIGN1c3RvbSB0eXBlIHByb3AgdG8gdGhlIEJ1dHRvbiBjb21wb25lbnRcclxuICAgIGNvbnN0IFtvcGVuLCBzZXRPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgY29uc3QgW3RpbWUsIHNldFRpbWVdID0gdXNlU3RhdGU8eyBob3VyOiBudW1iZXI7IG1pbnV0ZTogbnVtYmVyIH0gfCBudWxsPihcclxuICAgICAgICBudWxsLFxyXG4gICAgKVxyXG4gICAgY29uc3QgW3RvVGltZSwgc2V0VG9UaW1lXSA9IHVzZVN0YXRlPHtcclxuICAgICAgICBob3VyOiBudW1iZXJcclxuICAgICAgICBtaW51dGU6IG51bWJlclxyXG4gICAgfSB8IG51bGw+KG51bGwpXHJcblxyXG4gICAgLy8gU3RhdGUgdG8gdHJhY2sgcGVuZGluZyBzZWxlY3Rpb24gd2hlbiBjb25maXJtYXRpb24gYnV0dG9uIGlzIGVuYWJsZWRcclxuICAgIGNvbnN0IFtwZW5kaW5nU2VsZWN0aW9uLCBzZXRQZW5kaW5nU2VsZWN0aW9uXSA9IHVzZVN0YXRlPFxyXG4gICAgICAgIERhdGVSYW5nZSB8IERhdGUgfCB1bmRlZmluZWRcclxuICAgID4odmFsdWUgfHwgdW5kZWZpbmVkKVxyXG5cclxuICAgIC8vIFNldCBwbGFjZWhvbGRlciBiYXNlZCBvbiBtb2RlXHJcbiAgICBjb25zdCBhY3R1YWxQbGFjZWhvbGRlciA9XHJcbiAgICAgICAgbW9kZSA9PT0gJ3JhbmdlJyA/ICdTZWxlY3QgZGF0ZSByYW5nZScgOiAnU2VsZWN0IGRhdGUnXHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICAvLyBEZWVwIGVxdWFsaXR5IGNoZWNrIGZvciBkYXRlcyB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHVwZGF0ZXNcclxuICAgICAgICBjb25zdCBpc0VxdWFsID0gKGE6IGFueSwgYjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChhID09PSBiKSByZXR1cm4gdHJ1ZVxyXG5cclxuICAgICAgICAgICAgLy8gSGFuZGxlIERhdGUgb2JqZWN0c1xyXG4gICAgICAgICAgICBpZiAoYSBpbnN0YW5jZW9mIERhdGUgJiYgYiBpbnN0YW5jZW9mIERhdGUpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBhLmdldFRpbWUoKSA9PT0gYi5nZXRUaW1lKClcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gSGFuZGxlIERhdGVSYW5nZSBvYmplY3RzXHJcbiAgICAgICAgICAgIGlmIChhICYmIGIgJiYgdHlwZW9mIGEgPT09ICdvYmplY3QnICYmIHR5cGVvZiBiID09PSAnb2JqZWN0Jykge1xyXG4gICAgICAgICAgICAgICAgaWYgKCdmcm9tJyBpbiBhICYmICd0bycgaW4gYSAmJiAnZnJvbScgaW4gYiAmJiAndG8nIGluIGIpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBmcm9tRXF1YWwgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhLmZyb20gJiYgYi5mcm9tXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGEuZnJvbSBpbnN0YW5jZW9mIERhdGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYi5mcm9tIGluc3RhbmNlb2YgRGF0ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhLmZyb20uZ2V0VGltZSgpID09PSBiLmZyb20uZ2V0VGltZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGEuZnJvbSA9PT0gYi5mcm9tXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvRXF1YWwgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhLnRvICYmIGIudG9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYS50byBpbnN0YW5jZW9mIERhdGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYi50byBpbnN0YW5jZW9mIERhdGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYS50by5nZXRUaW1lKCkgPT09IGIudG8uZ2V0VGltZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGEudG8gPT09IGIudG9cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZyb21FcXVhbCAmJiB0b0VxdWFsXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gT25seSB1cGRhdGUgaWYgdGhlIHZhbHVlIGhhcyBhY3R1YWxseSBjaGFuZ2VkXHJcbiAgICAgICAgaWYgKCFpc0VxdWFsKHZhbHVlLCBkYXRlVmFsdWUpKSB7XHJcbiAgICAgICAgICAgIGlmICh2YWx1ZSkge1xyXG4gICAgICAgICAgICAgICAgc2V0RGF0ZVZhbHVlKHZhbHVlKVxyXG4gICAgICAgICAgICAgICAgLy8gQWxzbyB1cGRhdGUgcGVuZGluZ1NlbGVjdGlvbiB3aXRoIHRoZSB2YWx1ZVxyXG4gICAgICAgICAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih2YWx1ZSlcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAodmFsdWUgaW5zdGFuY2VvZiBEYXRlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaG91cnMgPSB2YWx1ZS5nZXRIb3VycygpXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWludXRlcyA9IHZhbHVlLmdldE1pbnV0ZXMoKVxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChob3VycyB8fCBtaW51dGVzKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lKHsgaG91cjogaG91cnMsIG1pbnV0ZTogbWludXRlcyB9KVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmICgnZnJvbScgaW4gdmFsdWUgJiYgdmFsdWUuZnJvbSBpbnN0YW5jZW9mIERhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGZyb20sIHRvIH0gPSB2YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZyb21Ib3VycyA9IGZyb20uZ2V0SG91cnMoKVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZyb21NaW51dGVzID0gZnJvbS5nZXRNaW51dGVzKClcclxuICAgICAgICAgICAgICAgICAgICBpZiAoZnJvbUhvdXJzIHx8IGZyb21NaW51dGVzKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lKHsgaG91cjogZnJvbUhvdXJzLCBtaW51dGU6IGZyb21NaW51dGVzIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRvIGluc3RhbmNlb2YgRGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0b0hvdXJzID0gdG8uZ2V0SG91cnMoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0b01pbnV0ZXMgPSB0by5nZXRNaW51dGVzKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRvSG91cnMgfHwgdG9NaW51dGVzKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VG9UaW1lKHsgaG91cjogdG9Ib3VycywgbWludXRlOiB0b01pbnV0ZXMgfSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAvLyBXaGVuIHZhbHVlIGlzIG51bGwvdW5kZWZpbmVkLCByZXNldCB0byBpbml0aWFsIHN0YXRlIGJ1dCBtYWludGFpbiBmb3JtYXRcclxuICAgICAgICAgICAgICAgIHNldERhdGVWYWx1ZShcclxuICAgICAgICAgICAgICAgICAgICBtb2RlID09PSAncmFuZ2UnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8geyBmcm9tOiB1bmRlZmluZWQsIHRvOiB1bmRlZmluZWQgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIC8vIEFsc28gcmVzZXQgcGVuZGluZ1NlbGVjdGlvblxyXG4gICAgICAgICAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih1bmRlZmluZWQpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9LCBbdmFsdWUsIG1vZGVdKVxyXG5cclxuICAgIGNvbnN0IHZhbGlkYXRlRGF0ZSA9IChkYXRlOiBEYXRlKTogYm9vbGVhbiA9PiB7XHJcbiAgICAgICAgaWYgKCF2YWxpZGF0aW9uKSByZXR1cm4gdHJ1ZVxyXG4gICAgICAgIGNvbnN0IHsgbWluRGF0ZSwgbWF4RGF0ZSwgZGlzYWJsZWREYXRlcywgZGlzYWJsZWREYXlzT2ZXZWVrIH0gPVxyXG4gICAgICAgICAgICB2YWxpZGF0aW9uXHJcbiAgICAgICAgaWYgKG1pbkRhdGUgJiYgaXNCZWZvcmUoZGF0ZSwgbWluRGF0ZSkpIHJldHVybiBmYWxzZVxyXG4gICAgICAgIGlmIChtYXhEYXRlICYmIGlzQWZ0ZXIoZGF0ZSwgbWF4RGF0ZSkpIHJldHVybiBmYWxzZVxyXG4gICAgICAgIGlmIChcclxuICAgICAgICAgICAgZGlzYWJsZWREYXRlcz8uc29tZShcclxuICAgICAgICAgICAgICAgIChkaXNhYmxlZERhdGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgZm9ybWF0KGRpc2FibGVkRGF0ZSwgJ3l5eXktTU0tZGQnKSA9PT1cclxuICAgICAgICAgICAgICAgICAgICBmb3JtYXQoZGF0ZSwgJ3l5eXktTU0tZGQnKSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgIClcclxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlXHJcbiAgICAgICAgaWYgKGRpc2FibGVkRGF5c09mV2Vlaz8uaW5jbHVkZXMoZGF0ZS5nZXREYXkoKSkpIHJldHVybiBmYWxzZVxyXG4gICAgICAgIHJldHVybiB0cnVlXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgYXBwbHlUaW1lID0gKFxyXG4gICAgICAgIGRhdGU6IERhdGUgfCB1bmRlZmluZWQsXHJcbiAgICAgICAgdDogeyBob3VyOiBudW1iZXI7IG1pbnV0ZTogbnVtYmVyIH0gfCBudWxsLFxyXG4gICAgKSA9PlxyXG4gICAgICAgIGRhdGUgJiYgdFxyXG4gICAgICAgICAgICA/IHNldChkYXRlLCB7XHJcbiAgICAgICAgICAgICAgICAgIGhvdXJzOiB0LmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgIG1pbnV0ZXM6IHQubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICBzZWNvbmRzOiAwLFxyXG4gICAgICAgICAgICAgICAgICBtaWxsaXNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgOiBkYXRlXHJcblxyXG4gICAgY29uc3QgaGFuZGxlVmFsdWVDaGFuZ2UgPSAobmV3VmFsdWU6IERhdGVSYW5nZSB8IERhdGUgfCB1bmRlZmluZWQpID0+IHtcclxuICAgICAgICBpZiAoIW5ld1ZhbHVlKSB7XHJcbiAgICAgICAgICAgIC8vIFdoZW4gYSBkYXRlIGlzIHVuc2VsZWN0ZWQsIG1haW50YWluIHRoZSBmb3JtYXQgY29uc2lzdGVuY3lcclxuICAgICAgICAgICAgc2V0RGF0ZVZhbHVlKFxyXG4gICAgICAgICAgICAgICAgbW9kZSA9PT0gJ3JhbmdlJ1xyXG4gICAgICAgICAgICAgICAgICAgID8geyBmcm9tOiB1bmRlZmluZWQsIHRvOiB1bmRlZmluZWQgfVxyXG4gICAgICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24odW5kZWZpbmVkKVxyXG4gICAgICAgICAgICBvbkNoYW5nZShudWxsKVxyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChtb2RlID09PSAncmFuZ2UnKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHsgZnJvbSwgdG8gfSA9IG5ld1ZhbHVlIGFzIERhdGVSYW5nZVxyXG4gICAgICAgICAgICBpZiAoZnJvbSAmJiAhdmFsaWRhdGVEYXRlKGZyb20pKSByZXR1cm5cclxuICAgICAgICAgICAgaWYgKHRvICYmICF2YWxpZGF0ZURhdGUodG8pKSByZXR1cm5cclxuXHJcbiAgICAgICAgICAgIC8vIElmIGNvbmZpcm1hdGlvbiBpcyByZXF1aXJlZCwgc3RvcmUgdGhlIHNlbGVjdGlvbiBpbiBwZW5kaW5nIHN0YXRlXHJcbiAgICAgICAgICAgIGlmIChjb25maXJtU2VsZWN0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRQZW5kaW5nU2VsZWN0aW9uKHsgZnJvbSwgdG8gfSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHNldERhdGVWYWx1ZSh7IGZyb20sIHRvIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gSWYgdGltZSBpcyBub3Qgc2V0LCBpbml0aWFsaXplIGl0IHdpdGggY3VycmVudCB0aW1lXHJcbiAgICAgICAgICAgICAgICBsZXQgY3VycmVudFRpbWUgPSB0aW1lXHJcbiAgICAgICAgICAgICAgICBpZiAoIWN1cnJlbnRUaW1lICYmIHNob3VsZEluY2x1ZGVUaW1lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBub3cuZ2V0SG91cnMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiBub3cuZ2V0TWludXRlcygpLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBzZXRUaW1lKGN1cnJlbnRUaW1lKVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIGxldCBjdXJyZW50VG9UaW1lID0gdG9UaW1lXHJcbiAgICAgICAgICAgICAgICBpZiAoIWN1cnJlbnRUb1RpbWUgJiYgc2hvdWxkSW5jbHVkZVRpbWUgJiYgdG8pIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFRvVGltZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaG91cjogbm93LmdldEhvdXJzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZTogbm93LmdldE1pbnV0ZXMoKSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VG9UaW1lKGN1cnJlbnRUb1RpbWUpXHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2Uoe1xyXG4gICAgICAgICAgICAgICAgICAgIHN0YXJ0RGF0ZTogYXBwbHlUaW1lKGZyb20sIGN1cnJlbnRUaW1lKSxcclxuICAgICAgICAgICAgICAgICAgICBlbmREYXRlOiBhcHBseVRpbWUodG8sIGN1cnJlbnRUb1RpbWUpLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHNpbmdsZURhdGUgPSBuZXdWYWx1ZSBhcyBEYXRlXHJcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVEYXRlKHNpbmdsZURhdGUpKSByZXR1cm5cclxuXHJcbiAgICAgICAgICAgIC8vIElmIGNvbmZpcm1hdGlvbiBpcyByZXF1aXJlZCwgc3RvcmUgdGhlIHNlbGVjdGlvbiBpbiBwZW5kaW5nIHN0YXRlXHJcbiAgICAgICAgICAgIGlmIChjb25maXJtU2VsZWN0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRQZW5kaW5nU2VsZWN0aW9uKHNpbmdsZURhdGUpXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBzZXREYXRlVmFsdWUoc2luZ2xlRGF0ZSlcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBJZiB0aW1lIGlzIG5vdCBzZXQgYW5kIHdlIHNob3VsZCBpbmNsdWRlIHRpbWUsIHByZXNlcnZlIGV4aXN0aW5nIHRpbWUgb3IgaW5pdGlhbGl6ZVxyXG4gICAgICAgICAgICAgICAgbGV0IGN1cnJlbnRUaW1lID0gdGltZVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIElmIHdlIGhhdmUgYW4gZXhpc3RpbmcgZGF0ZVZhbHVlIHdpdGggdGltZSwgcHJlc2VydmUgdGhhdCB0aW1lXHJcbiAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgIWN1cnJlbnRUaW1lICYmXHJcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkSW5jbHVkZVRpbWUgJiZcclxuICAgICAgICAgICAgICAgICAgICBkYXRlVmFsdWUgaW5zdGFuY2VvZiBEYXRlXHJcbiAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50VGltZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaG91cjogZGF0ZVZhbHVlLmdldEhvdXJzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZTogZGF0ZVZhbHVlLmdldE1pbnV0ZXMoKSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VGltZShjdXJyZW50VGltZSlcclxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoIWN1cnJlbnRUaW1lICYmIHNob3VsZEluY2x1ZGVUaW1lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gT25seSB1c2UgY3VycmVudCB0aW1lIGlmIG5vIGV4aXN0aW5nIHRpbWUgaXMgYXZhaWxhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBub3cuZ2V0SG91cnMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiBub3cuZ2V0TWludXRlcygpLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBzZXRUaW1lKGN1cnJlbnRUaW1lKVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIEFsd2F5cyBhcHBseSB0aW1lIGlmIHNob3VsZEluY2x1ZGVUaW1lIGlzIHRydWVcclxuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHNob3VsZEluY2x1ZGVUaW1lXHJcbiAgICAgICAgICAgICAgICAgICAgPyBhcHBseVRpbWUoc2luZ2xlRGF0ZSwgY3VycmVudFRpbWUpXHJcbiAgICAgICAgICAgICAgICAgICAgOiBzaW5nbGVEYXRlXHJcblxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2UocmVzdWx0KVxyXG5cclxuICAgICAgICAgICAgICAgIGlmIChjbG9zZU9uU2VsZWN0ICYmICFzaG91bGRJbmNsdWRlVGltZSkgc2V0T3BlbihmYWxzZSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVUaW1lQ2hhbmdlID0gKGRhdGU6IERhdGUsIGlzVG9UaW1lID0gZmFsc2UpID0+IHtcclxuICAgICAgICBpZiAoIWRhdGUpIHJldHVyblxyXG5cclxuICAgICAgICBjb25zdCBuZXdUaW1lID0geyBob3VyOiBkYXRlLmdldEhvdXJzKCksIG1pbnV0ZTogZGF0ZS5nZXRNaW51dGVzKCkgfVxyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB0aGUgdGltZSBoYXMgYWN0dWFsbHkgY2hhbmdlZCBiZWZvcmUgdXBkYXRpbmcgc3RhdGVcclxuICAgICAgICBpZiAoaXNUb1RpbWUpIHtcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudFRvVGltZSA9IHRvVGltZVxyXG5cclxuICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgIWN1cnJlbnRUb1RpbWUgfHxcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUb1RpbWUuaG91ciAhPT0gbmV3VGltZS5ob3VyIHx8XHJcbiAgICAgICAgICAgICAgICBjdXJyZW50VG9UaW1lLm1pbnV0ZSAhPT0gbmV3VGltZS5taW51dGVcclxuICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRUb1RpbWUobmV3VGltZSlcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgcGVuZGluZ1NlbGVjdGlvbiB3aXRoIHRoZSBuZXcgdGltZVxyXG4gICAgICAgICAgICAgICAgaWYgKGNvbmZpcm1TZWxlY3Rpb24gJiYgKHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlKT8udG8pIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdFbmREYXRlID0gc2V0KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpLnRvISxcclxuICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaG91cnM6IG5ld1RpbWUuaG91cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZXM6IG5ld1RpbWUubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbGxpc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24oe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi4ocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0bzogbmV3RW5kRGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIElmIGNvbmZpcm1hdGlvbiBpcyBub3QgcmVxdWlyZWQsIGNhbGwgb25DaGFuZ2UgZGlyZWN0bHlcclxuICAgICAgICAgICAgICAgIGlmICghY29uZmlybVNlbGVjdGlvbikge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICgoZGF0ZVZhbHVlIGFzIERhdGVSYW5nZSk/LnRvKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0VuZERhdGUgPSBzZXQoKGRhdGVWYWx1ZSBhcyBEYXRlUmFuZ2UpLnRvISwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaG91cnM6IG5ld1RpbWUuaG91cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZXM6IG5ld1RpbWUubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbGxpc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXJ0RGF0ZTogKGRhdGVWYWx1ZSBhcyBEYXRlUmFuZ2UpLmZyb20sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbmREYXRlOiBuZXdFbmREYXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gdGltZVxyXG5cclxuICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgIWN1cnJlbnRUaW1lIHx8XHJcbiAgICAgICAgICAgICAgICBjdXJyZW50VGltZS5ob3VyICE9PSBuZXdUaW1lLmhvdXIgfHxcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lLm1pbnV0ZSAhPT0gbmV3VGltZS5taW51dGVcclxuICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRUaW1lKG5ld1RpbWUpXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gVXBkYXRlIHBlbmRpbmdTZWxlY3Rpb24gd2l0aCB0aGUgbmV3IHRpbWVcclxuICAgICAgICAgICAgICAgIGlmIChjb25maXJtU2VsZWN0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKChwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZSk/LmZyb20pIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3U3RhcnREYXRlID0gc2V0KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlKS5mcm9tISxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyczogbmV3VGltZS5ob3VyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZXM6IG5ld1RpbWUubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlsbGlzZWNvbmRzOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi4ocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZnJvbTogbmV3U3RhcnREYXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocGVuZGluZ1NlbGVjdGlvbiBpbnN0YW5jZW9mIERhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3RGF0ZSA9IHNldChwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGUsIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXJzOiBuZXdUaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW51dGVzOiBuZXdUaW1lLm1pbnV0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaWxsaXNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRQZW5kaW5nU2VsZWN0aW9uKG5ld0RhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIElmIGNvbmZpcm1hdGlvbiBpcyBub3QgcmVxdWlyZWQsIGNhbGwgb25DaGFuZ2UgZGlyZWN0bHlcclxuICAgICAgICAgICAgICAgIGlmICghY29uZmlybVNlbGVjdGlvbikge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICgoZGF0ZVZhbHVlIGFzIERhdGVSYW5nZSk/LmZyb20pIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3U3RhcnREYXRlID0gc2V0KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKGRhdGVWYWx1ZSBhcyBEYXRlUmFuZ2UpLmZyb20hLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXJzOiBuZXdUaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlczogbmV3VGltZS5taW51dGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaWxsaXNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGFydERhdGU6IG5ld1N0YXJ0RGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVuZERhdGU6IChkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlKS50byxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGVWYWx1ZSBpbnN0YW5jZW9mIERhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3RGF0ZSA9IHNldChkYXRlVmFsdWUsIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXJzOiBuZXdUaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW51dGVzOiBuZXdUaW1lLm1pbnV0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaWxsaXNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZShuZXdEYXRlKVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVDbGVhciA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxCdXR0b25FbGVtZW50PikgPT4ge1xyXG4gICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCkgLy8gUHJldmVudCB0cmlnZ2VyaW5nIHRoZSBwb3BvdmVyXHJcbiAgICAgICAgc2V0RGF0ZVZhbHVlKFxyXG4gICAgICAgICAgICBtb2RlID09PSAncmFuZ2UnID8geyBmcm9tOiB1bmRlZmluZWQsIHRvOiB1bmRlZmluZWQgfSA6IHVuZGVmaW5lZCxcclxuICAgICAgICApXHJcbiAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih1bmRlZmluZWQpXHJcbiAgICAgICAgc2V0VGltZShudWxsKVxyXG4gICAgICAgIHNldFRvVGltZShudWxsKVxyXG4gICAgICAgIG9uQ2hhbmdlKG51bGwpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gRnVuY3Rpb24gdG8gaGFuZGxlIGNsZWFyIGJ1dHRvbiBjbGljayBpbnNpZGUgdGhlIHBvcG92ZXJcclxuICAgIGNvbnN0IGhhbmRsZUNsZWFySW5Qb3BvdmVyID0gKCkgPT4ge1xyXG4gICAgICAgIHNldERhdGVWYWx1ZShcclxuICAgICAgICAgICAgbW9kZSA9PT0gJ3JhbmdlJyA/IHsgZnJvbTogdW5kZWZpbmVkLCB0bzogdW5kZWZpbmVkIH0gOiB1bmRlZmluZWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24odW5kZWZpbmVkKVxyXG4gICAgICAgIHNldFRpbWUobnVsbClcclxuICAgICAgICBzZXRUb1RpbWUobnVsbClcclxuICAgICAgICBvbkNoYW5nZShudWxsKVxyXG4gICAgICAgIC8vIENsb3NlIHRoZSBwb3BvdmVyIGFmdGVyIGNsZWFyaW5nXHJcbiAgICAgICAgc2V0T3BlbihmYWxzZSlcclxuICAgIH1cclxuXHJcbiAgICAvLyBGdW5jdGlvbiB0byBoYW5kbGUgY29uZmlybWF0aW9uIGJ1dHRvbiBjbGlja1xyXG4gICAgY29uc3QgaGFuZGxlQ29uZmlybSA9ICgpID0+IHtcclxuICAgICAgICBpZiAoIXBlbmRpbmdTZWxlY3Rpb24pIHJldHVyblxyXG5cclxuICAgICAgICBpZiAobW9kZSA9PT0gJ3JhbmdlJykge1xyXG4gICAgICAgICAgICBjb25zdCB7IGZyb20sIHRvIH0gPSBwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZVxyXG4gICAgICAgICAgICBzZXREYXRlVmFsdWUoeyBmcm9tLCB0byB9KVxyXG5cclxuICAgICAgICAgICAgLy8gQXBwbHkgdGltZSBpZiBuZWVkZWRcclxuICAgICAgICAgICAgbGV0IGN1cnJlbnRUaW1lID0gdGltZVxyXG4gICAgICAgICAgICBpZiAoIWN1cnJlbnRUaW1lICYmIHNob3VsZEluY2x1ZGVUaW1lICYmIGZyb20pIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0geyBob3VyOiBub3cuZ2V0SG91cnMoKSwgbWludXRlOiBub3cuZ2V0TWludXRlcygpIH1cclxuICAgICAgICAgICAgICAgIHNldFRpbWUoY3VycmVudFRpbWUpXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGxldCBjdXJyZW50VG9UaW1lID0gdG9UaW1lXHJcbiAgICAgICAgICAgIGlmICghY3VycmVudFRvVGltZSAmJiBzaG91bGRJbmNsdWRlVGltZSAmJiB0bykge1xyXG4gICAgICAgICAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgY3VycmVudFRvVGltZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICBob3VyOiBub3cuZ2V0SG91cnMoKSxcclxuICAgICAgICAgICAgICAgICAgICBtaW51dGU6IG5vdy5nZXRNaW51dGVzKCksXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBzZXRUb1RpbWUoY3VycmVudFRvVGltZSlcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgb25DaGFuZ2Uoe1xyXG4gICAgICAgICAgICAgICAgc3RhcnREYXRlOiBhcHBseVRpbWUoZnJvbSwgY3VycmVudFRpbWUpLFxyXG4gICAgICAgICAgICAgICAgZW5kRGF0ZTogYXBwbHlUaW1lKHRvLCBjdXJyZW50VG9UaW1lKSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb25zdCBzaW5nbGVEYXRlID0gcGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlXHJcbiAgICAgICAgICAgIHNldERhdGVWYWx1ZShzaW5nbGVEYXRlKVxyXG5cclxuICAgICAgICAgICAgLy8gQXBwbHkgdGltZSBpZiBuZWVkZWRcclxuICAgICAgICAgICAgbGV0IGN1cnJlbnRUaW1lID0gdGltZVxyXG5cclxuICAgICAgICAgICAgLy8gSWYgd2UgaGF2ZSBhbiBleGlzdGluZyBkYXRlVmFsdWUgd2l0aCB0aW1lLCBwcmVzZXJ2ZSB0aGF0IHRpbWVcclxuICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgIWN1cnJlbnRUaW1lICYmXHJcbiAgICAgICAgICAgICAgICBzaG91bGRJbmNsdWRlVGltZSAmJlxyXG4gICAgICAgICAgICAgICAgZGF0ZVZhbHVlIGluc3RhbmNlb2YgRGF0ZVxyXG4gICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIGhvdXI6IGRhdGVWYWx1ZS5nZXRIb3VycygpLFxyXG4gICAgICAgICAgICAgICAgICAgIG1pbnV0ZTogZGF0ZVZhbHVlLmdldE1pbnV0ZXMoKSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHNldFRpbWUoY3VycmVudFRpbWUpXHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoIWN1cnJlbnRUaW1lICYmIHNob3VsZEluY2x1ZGVUaW1lKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBPbmx5IHVzZSBjdXJyZW50IHRpbWUgaWYgbm8gZXhpc3RpbmcgdGltZSBpcyBhdmFpbGFibGVcclxuICAgICAgICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0geyBob3VyOiBub3cuZ2V0SG91cnMoKSwgbWludXRlOiBub3cuZ2V0TWludXRlcygpIH1cclxuICAgICAgICAgICAgICAgIHNldFRpbWUoY3VycmVudFRpbWUpXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHNob3VsZEluY2x1ZGVUaW1lXHJcbiAgICAgICAgICAgICAgICA/IGFwcGx5VGltZShzaW5nbGVEYXRlLCBjdXJyZW50VGltZSlcclxuICAgICAgICAgICAgICAgIDogc2luZ2xlRGF0ZVxyXG5cclxuICAgICAgICAgICAgb25DaGFuZ2UocmVzdWx0KVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQ2xvc2UgdGhlIHBvcG92ZXIgYWZ0ZXIgY29uZmlybWF0aW9uXHJcbiAgICAgICAgc2V0T3BlbihmYWxzZSlcclxuICAgICAgICAvLyBSZXNldCBwZW5kaW5nIHNlbGVjdGlvblxyXG4gICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24odW5kZWZpbmVkKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIHRpbWVPcHRpb25zIHJlbW92ZWQgYXMgd2UncmUgbm93IHVzaW5nIFRpbWVQaWNrZXIgY29tcG9uZW50XHJcblxyXG4gICAgLy8gRGV0ZXJtaW5lIGlmIHdlIHNob3VsZCBpbmNsdWRlIHRpbWUgYmFzZWQgb24gdGhlIHR5cGUgcHJvcCBvciB0aGUgZGVwcmVjYXRlZCBpbmNsdWRlVGltZSBwcm9wXHJcbiAgICBjb25zdCBbc2hvdWxkSW5jbHVkZVRpbWUsIHNldFNob3VsZEluY2x1ZGVUaW1lXSA9IHVzZVN0YXRlKFxyXG4gICAgICAgIHR5cGUgPT09ICdkYXRldGltZScgfHwgaW5jbHVkZVRpbWUsXHJcbiAgICApXHJcblxyXG4gICAgLy8gVXBkYXRlIHNob3VsZEluY2x1ZGVUaW1lIHdoZW4gdHlwZSBvciBpbmNsdWRlVGltZSBjaGFuZ2VzXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIHNldFNob3VsZEluY2x1ZGVUaW1lKHR5cGUgPT09ICdkYXRldGltZScgfHwgaW5jbHVkZVRpbWUpXHJcbiAgICB9LCBbdHlwZSwgaW5jbHVkZVRpbWVdKVxyXG5cclxuICAgIGNvbnN0IGRpc3BsYXlUaW1lRm9ybWF0ID0gc2hvdWxkSW5jbHVkZVRpbWVcclxuICAgICAgICA/IGAke2RhdGVGb3JtYXR9ICR7dGltZUZvcm1hdH1gXHJcbiAgICAgICAgOiBkYXRlRm9ybWF0XHJcblxyXG4gICAgLy8gR3VhcmQgYWdhaW5zdCBpbnZhbGlkIGRhdGVzXHJcbiAgICBjb25zdCBmb3JtYXREYXRlV2l0aFRpbWUgPSAoZGF0ZTogRGF0ZSB8IHVuZGVmaW5lZCkgPT4ge1xyXG4gICAgICAgIGlmICghZGF0ZSkgcmV0dXJuICcnXHJcbiAgICAgICAgY29uc3QgdmFsaWREYXRlID0gZGF0ZSBpbnN0YW5jZW9mIERhdGUgPyBkYXRlIDogbmV3IERhdGUoKVxyXG4gICAgICAgIHJldHVybiBpc05hTih2YWxpZERhdGUuZ2V0VGltZSgpKVxyXG4gICAgICAgICAgICA/ICcnXHJcbiAgICAgICAgICAgIDogZm9ybWF0KHZhbGlkRGF0ZSwgZGlzcGxheVRpbWVGb3JtYXQpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gVGltZSBwaWNrZXIgaW1wbGVtZW50YXRpb24gbm93IHVzZXMgdGhlIFRpbWVQaWNrZXIgY29tcG9uZW50IHdpdGggbW9kZSBvcHRpb25cclxuXHJcbiAgICBpZiAoZGlzYWJsZWQpIHtcclxuICAgICAgICAvLyBVc2UgdGhlIHByb3ZpZGVkIHZhbHVlIGlmIGF2YWlsYWJsZSwgb3RoZXJ3aXNlIHVzZSBjdXJyZW50IGRhdGVcclxuICAgICAgICBjb25zdCBkaXNwbGF5RGF0ZSA9IChkYXRlOiBEYXRlIHwgdW5kZWZpbmVkKSA9PiB7XHJcbiAgICAgICAgICAgIGlmICghZGF0ZSB8fCAhKGRhdGUgaW5zdGFuY2VvZiBEYXRlKSB8fCBpc05hTihkYXRlLmdldFRpbWUoKSkpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBmb3JtYXQobmV3IERhdGUoKSwgZGF0ZUZvcm1hdClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gZm9ybWF0KFxyXG4gICAgICAgICAgICAgICAgZGF0ZSxcclxuICAgICAgICAgICAgICAgIHNob3VsZEluY2x1ZGVUaW1lID8gZGlzcGxheVRpbWVGb3JtYXQgOiBkYXRlRm9ybWF0LFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBGb3JtYXQgdGhlIGRhdGUgYmFzZWQgb24gdGhlIG1vZGUgYW5kIHZhbHVlXHJcbiAgICAgICAgbGV0IGRpc3BsYXlWYWx1ZVxyXG4gICAgICAgIGlmIChtb2RlID09PSAncmFuZ2UnKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJhbmdlID0gZGF0ZVZhbHVlIGFzIERhdGVSYW5nZVxyXG4gICAgICAgICAgICBpZiAocmFuZ2U/LmZyb20pIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXlWYWx1ZSA9IHJhbmdlLnRvXHJcbiAgICAgICAgICAgICAgICAgICAgPyBgJHtkaXNwbGF5RGF0ZShyYW5nZS5mcm9tKX0gLSAke2Rpc3BsYXlEYXRlKHJhbmdlLnRvKX1gXHJcbiAgICAgICAgICAgICAgICAgICAgOiBkaXNwbGF5RGF0ZShyYW5nZS5mcm9tKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgY3VycmVudEZvcm1hdHRlZCA9IGZvcm1hdChuZXcgRGF0ZSgpLCBkYXRlRm9ybWF0KVxyXG4gICAgICAgICAgICAgICAgZGlzcGxheVZhbHVlID0gYCR7Y3VycmVudEZvcm1hdHRlZH0gLSAke2N1cnJlbnRGb3JtYXR0ZWR9YFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgZGlzcGxheVZhbHVlID1cclxuICAgICAgICAgICAgICAgIGRhdGVWYWx1ZSBpbnN0YW5jZW9mIERhdGVcclxuICAgICAgICAgICAgICAgICAgICA/IGRpc3BsYXlEYXRlKGRhdGVWYWx1ZSBhcyBEYXRlKVxyXG4gICAgICAgICAgICAgICAgICAgIDogZm9ybWF0KG5ldyBEYXRlKCksIGRhdGVGb3JtYXQpXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAge2xhYmVsICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgYXNDaGlsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbj17bGFiZWxQb3NpdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2xhYmVsfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKCdweC00IGp1c3RpZnktc3RhcnQgdy1mdWxsJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGljb25MZWZ0PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb24gPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUmVhY3QuaXNWYWxpZEVsZW1lbnQoaWNvbikgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlYWN0LmNsb25lRWxlbWVudChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb24gYXMgUmVhY3QuUmVhY3RFbGVtZW50LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ21yLVs4LjVweF0gdy01IGgtNSB0ZXh0LW5ldXRyYWwtNDAwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZWFjdC5jcmVhdGVFbGVtZW50KGljb24gYXMgYW55LCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplOiAyMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnbXItWzguNXB4XSB3LTUgaC01IHRleHQtbmV1dHJhbC00MDAnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFySWNvbiBjbGFzc05hbWU9XCJtci1bOC41cHhdIHctNSBoLTUgdGV4dC1uZXV0cmFsLTQwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgey4uLmJ1dHRvblByb3BzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtkaXNwbGF5VmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlbmRlckJ1dHRvbkxhYmVsID0gKCkgPT4ge1xyXG4gICAgICAgIGlmIChtb2RlID09PSAncmFuZ2UnKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJhbmdlID0gZGF0ZVZhbHVlIGFzIERhdGVSYW5nZVxyXG4gICAgICAgICAgICBpZiAocmFuZ2U/LmZyb20pIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiByYW5nZS50byA/IChcclxuICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZVdpdGhUaW1lKHJhbmdlLmZyb20pfSAteycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERhdGVXaXRoVGltZShyYW5nZS50byl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgIGZvcm1hdERhdGVXaXRoVGltZShyYW5nZS5mcm9tKVxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8vIFVzZSBjb25zaXN0ZW50IGRhdGUgZm9ybWF0IGZvciBwbGFjZWhvbGRlclxyXG4gICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2FjdHVhbFBsYWNlaG9sZGVyfVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgfSBlbHNlIGlmIChkYXRlVmFsdWUpIHtcclxuICAgICAgICAgICAgcmV0dXJuIGZvcm1hdERhdGVXaXRoVGltZShkYXRlVmFsdWUgYXMgRGF0ZSlcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gVXNlIGNvbnNpc3RlbnQgZGF0ZSBmb3JtYXQgZm9yIHBsYWNlaG9sZGVyXHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+e2FjdHVhbFBsYWNlaG9sZGVyfTwvc3Bhbj5cclxuICAgICAgICApXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICB7bGFiZWwgJiYgKFxyXG4gICAgICAgICAgICAgICAgPExhYmVsIGlkPVwiZGF0ZVwiIHBvc2l0aW9uPXtsYWJlbFBvc2l0aW9ufSBkaXNhYmxlZD17ZGlzYWJsZWR9PlxyXG4gICAgICAgICAgICAgICAgICAgIHtsYWJlbH1cclxuICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDxQb3BvdmVyXHJcbiAgICAgICAgICAgICAgICBtb2RhbD17bW9kYWx9XHJcbiAgICAgICAgICAgICAgICBvcGVuPXtvcGVufVxyXG4gICAgICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXsoaXNPcGVuKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gV2hlbiBvcGVuaW5nIHRoZSBwb3BvdmVyLCBpbml0aWFsaXplIHBlbmRpbmdTZWxlY3Rpb24gd2l0aCB0aGUgY3VycmVudCB2YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChpc09wZW4gJiYgIXBlbmRpbmdTZWxlY3Rpb24gJiYgZGF0ZVZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24oZGF0ZVZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBzZXRPcGVuKGlzT3BlbilcclxuICAgICAgICAgICAgICAgIH19PlxyXG4gICAgICAgICAgICAgICAgPFBvcG92ZXJUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJkYXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb25MZWZ0PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZWFjdC5pc1ZhbGlkRWxlbWVudChpY29uKSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlYWN0LmNyZWF0ZUVsZW1lbnQoaWNvbiBhcyBhbnksIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplOiAyMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU6ICd0ZXh0LW11dGVkLWZvcmVncm91bmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhckljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9ezIwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTQwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAncHgtNCBqdXN0aWZ5LXN0YXJ0IHctZnVsbCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlICYmICdwci0xMCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey4uLmJ1dHRvblByb3BzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlbmRlckJ1dHRvbkxhYmVsKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Y2xlYXJhYmxlICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZGF0ZVZhbHVlIGluc3RhbmNlb2YgRGF0ZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlKT8uZnJvbSkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMSB0b3AtMS8yIC10cmFuc2xhdGUteS0xLzIgaC04IHctOCBwLTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGVhcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkNsZWFyIGRhdGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9ezE2fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTQwMCBob3Zlcjp0ZXh0LWJhY2tncm91bmQwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgPFBvcG92ZXJDb250ZW50IGNsYXNzTmFtZT1cInctYXV0byBwLTBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7bW9kZSA9PT0gJ3JhbmdlJyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT1cInJhbmdlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsRm9jdXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlPVwicmFuZ2VcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1vbnRoPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGNvbmZpcm1TZWxlY3Rpb24gJiYgcGVuZGluZ1NlbGVjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpPy5mcm9tXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IChkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlKT8uZnJvbSkgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGxvd0Z1dHVyZVllYXJzPXthbGxvd0Z1dHVyZVllYXJzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dZZWFyUGlja2VyPXtzaG93WWVhclBpY2tlcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpcm1TZWxlY3Rpb24gJiYgcGVuZGluZ1NlbGVjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IChkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblNlbGVjdD17KHZhbHVlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEVuc3VyZSB3ZSBtYWludGFpbiB0aGUgZGF0ZSBmb3JtYXQgd2hlbiBhIGRhdGUgaXMgdW5zZWxlY3RlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVWYWx1ZUNoYW5nZSh2YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG51bWJlck9mTW9udGhzPXtudW1iZXJPZk1vbnRoc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaG93V2Vla051bWJlcj17c2hvd1dlZWtOdW1iZXJzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsaWRhdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAoZGF0ZSkgPT4gIXZhbGlkYXRlRGF0ZShkYXRlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB1bmRlZmluZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Nob3VsZEluY2x1ZGVUaW1lICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBib3JkZXItdCBib3JkZXItYm9yZGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRpbWVQaWNrZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17KCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gV2hlbiBjb25maXJtYXRpb24gaXMgZW5hYmxlZCwgdXNlIHRpbWUgZnJvbSBwZW5kaW5nU2VsZWN0aW9uIGlmIGF2YWlsYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgY3VycmVudFRpbWUgPSB0aW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpcm1TZWxlY3Rpb24gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKT8uZnJvbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZyb21EYXRlID0gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLmZyb20hXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VGltZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBmcm9tRGF0ZS5nZXRIb3VycygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZTogZnJvbURhdGUuZ2V0TWludXRlcygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoY3VycmVudFRpbWUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlLnNldEhvdXJzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRpbWUubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9WYWx1ZT17KCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gV2hlbiBjb25maXJtYXRpb24gaXMgZW5hYmxlZCwgdXNlIHRpbWUgZnJvbSBwZW5kaW5nU2VsZWN0aW9uIGlmIGF2YWlsYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgY3VycmVudFRvVGltZSA9IHRvVGltZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25maXJtU2VsZWN0aW9uICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk/LnRvXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdG9EYXRlID0gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRvIVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRvVGltZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyOiB0b0RhdGUuZ2V0SG91cnMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW51dGU6IHRvRGF0ZS5nZXRNaW51dGVzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjdXJyZW50VG9UaW1lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZS5zZXRIb3VycyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VG9UaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRvVGltZS5taW51dGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdW5kZWZpbmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkoKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVGltZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblRvQ2hhbmdlPXsoZGF0ZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVGltZUNoYW5nZShkYXRlLCB0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBBbHdheXMgZW5hYmxlIHRpbWUgcGlja2VyIHJlZ2FyZGxlc3Mgb2Ygd2hldGhlciBhIGRhdGUgaXMgc2VsZWN0ZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlPXt0aW1lTW9kZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpbWVNb2RlID09PSAncmFuZ2UnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdUaW1lIFJhbmdlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB1bmRlZmluZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlybVNlbGVjdGlvbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlcGFyYXRvciBjbGFzc05hbWU9XCJteS0wXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAncC0zIGZsZXggZ2FwLTMganVzdGlmeS1lbmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2xlYXJhYmxlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZml0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNsZWFySW5Qb3BvdmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uTGVmdD17WH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZml0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJDbGVhciBkYXRlIHJhbmdlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDbGVhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29uZmlybX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXBlbmRpbmdTZWxlY3Rpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e0NoZWNrfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlybUJ1dHRvblRleHR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PVwic2luZ2xlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsRm9jdXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlPVwic2luZ2xlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb250aD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChjb25maXJtU2VsZWN0aW9uICYmIHBlbmRpbmdTZWxlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gKHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogKGRhdGVWYWx1ZSBhcyBEYXRlKSkgfHwgbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaG93WWVhclBpY2tlcj17c2hvd1llYXJQaWNrZXJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxsb3dGdXR1cmVZZWFycz17YWxsb3dGdXR1cmVZZWFyc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpcm1TZWxlY3Rpb24gJiYgcGVuZGluZ1NlbGVjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAoZGF0ZVZhbHVlIGFzIERhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uU2VsZWN0PXsodmFsdWUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRW5zdXJlIHdlIG1haW50YWluIHRoZSBkYXRlIGZvcm1hdCB3aGVuIGEgZGF0ZSBpcyB1bnNlbGVjdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVZhbHVlQ2hhbmdlKHZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVtYmVyT2ZNb250aHM9e251bWJlck9mTW9udGhzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dXZWVrTnVtYmVyPXtzaG93V2Vla051bWJlcnN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWxpZGF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChkYXRlKSA9PiAhdmFsaWRhdGVEYXRlKGRhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvdWxkSW5jbHVkZVRpbWUgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJvcmRlci10IGJvcmRlci1ib3JkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGltZVBpY2tlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXsoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBXaGVuIGNvbmZpcm1hdGlvbiBpcyBlbmFibGVkLCB1c2UgdGltZSBmcm9tIHBlbmRpbmdTZWxlY3Rpb24gaWYgYXZhaWxhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBjdXJyZW50VGltZSA9IHRpbWVcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpcm1TZWxlY3Rpb24gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlbmRpbmdTZWxlY3Rpb24gaW5zdGFuY2VvZlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERhdGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICFpc05hTihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwZW5kaW5nU2VsZWN0aW9uLmdldFRpbWUoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VGltZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBwZW5kaW5nU2VsZWN0aW9uLmdldEhvdXJzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiBwZW5kaW5nU2VsZWN0aW9uLmdldE1pbnV0ZXMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRUaW1lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZS5zZXRIb3VycyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VGltZS5ob3VyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lLm1pbnV0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB1bmRlZmluZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvVmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b1RpbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gKCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhdGUgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0SG91cnMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9UaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9UaW1lLm1pbnV0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVGltZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblRvQ2hhbmdlPXsoZGF0ZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVGltZUNoYW5nZShkYXRlLCB0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBBbHdheXMgZW5hYmxlIHRpbWUgcGlja2VyIHJlZ2FyZGxlc3Mgb2Ygd2hldGhlciBhIGRhdGUgaXMgc2VsZWN0ZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlPXt0aW1lTW9kZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpbWVNb2RlID09PSAncmFuZ2UnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdUaW1lIFJhbmdlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB1bmRlZmluZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlybVNlbGVjdGlvbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlcGFyYXRvciBjbGFzc05hbWU9XCJteS0wXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAncC0zIGZsZXggZ2FwLTMganVzdGlmeS1lbmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2xlYXJhYmxlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZml0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNsZWFySW5Qb3BvdmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uTGVmdD17WH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJDbGVhciBkYXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDbGVhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29uZmlybX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXBlbmRpbmdTZWxlY3Rpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlID8gJycgOiAndy1mdWxsJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlybUJ1dHRvblRleHR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9Qb3BvdmVyQ29udGVudD5cclxuICAgICAgICAgICAgPC9Qb3BvdmVyPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBEYXRlUGlja2VyXHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ2FsZW5kYXIiLCJQb3BvdmVyIiwiUG9wb3ZlckNvbnRlbnQiLCJQb3BvdmVyVHJpZ2dlciIsIkNhbGVuZGFySWNvbiIsIlgiLCJDaGVjayIsIlRpbWVQaWNrZXIiLCJjbiIsImZvcm1hdCIsImlzQWZ0ZXIiLCJpc0JlZm9yZSIsInNldCIsIkxhYmVsIiwiU2VwYXJhdG9yIiwiRGF0ZVBpY2tlciIsIm9uQ2hhbmdlIiwiY2xhc3NOYW1lIiwicGxhY2Vob2xkZXIiLCJtb2RlIiwidHlwZSIsImRpc2FibGVkIiwidmFsdWUiLCJkYXRlRm9ybWF0IiwidmFsaWRhdGlvbiIsIm51bWJlck9mTW9udGhzIiwiY2xvc2VPblNlbGVjdCIsInNob3dXZWVrTnVtYmVycyIsInNob3dZZWFyUGlja2VyIiwiYWxsb3dGdXR1cmVZZWFycyIsImluY2x1ZGVUaW1lIiwidGltZU1vZGUiLCJ0aW1lRm9ybWF0IiwidGltZUludGVydmFsIiwibGFiZWwiLCJsYWJlbFBvc2l0aW9uIiwiY2xlYXJhYmxlIiwiaWNvbiIsImNvbmZpcm1TZWxlY3Rpb24iLCJjb25maXJtQnV0dG9uVGV4dCIsIm1vZGFsIiwiYnV0dG9uUHJvcHMiLCJkYXRlVmFsdWUiLCJzZXREYXRlVmFsdWUiLCJmcm9tIiwidW5kZWZpbmVkIiwidG8iLCJvcGVuIiwic2V0T3BlbiIsInRpbWUiLCJzZXRUaW1lIiwidG9UaW1lIiwic2V0VG9UaW1lIiwicGVuZGluZ1NlbGVjdGlvbiIsInNldFBlbmRpbmdTZWxlY3Rpb24iLCJhY3R1YWxQbGFjZWhvbGRlciIsImlzRXF1YWwiLCJhIiwiYiIsIkRhdGUiLCJnZXRUaW1lIiwiZnJvbUVxdWFsIiwidG9FcXVhbCIsImhvdXJzIiwiZ2V0SG91cnMiLCJtaW51dGVzIiwiZ2V0TWludXRlcyIsImhvdXIiLCJtaW51dGUiLCJmcm9tSG91cnMiLCJmcm9tTWludXRlcyIsInRvSG91cnMiLCJ0b01pbnV0ZXMiLCJ2YWxpZGF0ZURhdGUiLCJkYXRlIiwibWluRGF0ZSIsIm1heERhdGUiLCJkaXNhYmxlZERhdGVzIiwiZGlzYWJsZWREYXlzT2ZXZWVrIiwic29tZSIsImRpc2FibGVkRGF0ZSIsImluY2x1ZGVzIiwiZ2V0RGF5IiwiYXBwbHlUaW1lIiwidCIsInNlY29uZHMiLCJtaWxsaXNlY29uZHMiLCJoYW5kbGVWYWx1ZUNoYW5nZSIsIm5ld1ZhbHVlIiwiY3VycmVudFRpbWUiLCJzaG91bGRJbmNsdWRlVGltZSIsIm5vdyIsImN1cnJlbnRUb1RpbWUiLCJzdGFydERhdGUiLCJlbmREYXRlIiwic2luZ2xlRGF0ZSIsInJlc3VsdCIsImhhbmRsZVRpbWVDaGFuZ2UiLCJpc1RvVGltZSIsIm5ld1RpbWUiLCJuZXdFbmREYXRlIiwibmV3U3RhcnREYXRlIiwibmV3RGF0ZSIsImhhbmRsZUNsZWFyIiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsImhhbmRsZUNsZWFySW5Qb3BvdmVyIiwiaGFuZGxlQ29uZmlybSIsInNldFNob3VsZEluY2x1ZGVUaW1lIiwiZGlzcGxheVRpbWVGb3JtYXQiLCJmb3JtYXREYXRlV2l0aFRpbWUiLCJ2YWxpZERhdGUiLCJpc05hTiIsImRpc3BsYXlEYXRlIiwiZGlzcGxheVZhbHVlIiwicmFuZ2UiLCJjdXJyZW50Rm9ybWF0dGVkIiwiZGl2IiwiYXNDaGlsZCIsImlkIiwicG9zaXRpb24iLCJ2YXJpYW50IiwiaWNvbkxlZnQiLCJpc1ZhbGlkRWxlbWVudCIsImNsb25lRWxlbWVudCIsImNyZWF0ZUVsZW1lbnQiLCJzaXplIiwicmVuZGVyQnV0dG9uTGFiZWwiLCJzcGFuIiwib25PcGVuQ2hhbmdlIiwiaXNPcGVuIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJpbml0aWFsRm9jdXMiLCJtb250aCIsInNlbGVjdGVkIiwib25TZWxlY3QiLCJzaG93V2Vla051bWJlciIsImZyb21EYXRlIiwic2V0SG91cnMiLCJ0b1ZhbHVlIiwidG9EYXRlIiwib25Ub0NoYW5nZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});