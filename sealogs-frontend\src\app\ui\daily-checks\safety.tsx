'use client'
import React, { useEffect, useRef, useState } from 'react'
import dayjs from 'dayjs'
import { useMutation, useLazyQuery } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'

import {
    DailyCheckField,
    CheckFieldTopContent,
    CheckFieldContent,
    CheckField,
} from '@/components/daily-check-field'
import {
    GET_SECTION_MEMBER_COMMENTS,
    GET_SECTION_MEMBER_IMAGES,
    VesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import { useSearchParams } from 'next/navigation'
import { getLogBookEntryByID } from '@/app/lib/actions'
import {
    displayDescription,
    displayField,
    getFiltered<PERSON>ields,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'

import 'react-quill/dist/quill.snow.css'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import { getSafetyCheckFields } from '@/app/lib/dailyCheckFields'
import { sortCustomisedComponentFields } from '../vessels/actions'
import CrewChecker from './crew-checker'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { Card } from '@/components/ui/card'
import { ActionFooter } from '@/components/ui/action-footer'
import { useQueryState } from 'nuqs'
import { getDailyCheckNotification } from './actions'
import { useDebounceFn } from '@reactuses/core'

export default function Safety({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages?: any
    refreshImages?: any
}) {
    const logBookModel = new LogBookEntryModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    const commentModel = new SectionMemberCommentModel()
    const memberModel = new SeaLogsMemberModel()
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [logbook, setLogbook] = useState([] as any)
    const [sectionComment, setSectionComment] = useState('')
    // const commentRef = useRef<string>('')
    // const commentSectionRef = useRef<any>(null)

    // Use nuqs to directly update the URL parameter for tab navigation
    const [, setSecondTab] = useQueryState('checkTab')

    // Function to handle tab changes - works with both legacy setTab prop and direct URL updates
    const handleSetTab = (tab: string) => {
        // Update the URL parameter
        setSecondTab(tab)
    }

    const handleSetLogbook = async (logbook: any) => {
        setLogbook(logbook)
    }

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [tab])

    // Load logbook data if not offline
    if (!offline) {
        getLogBookEntryByID(+logentryID, handleSetLogbook)
    }

    const [saving, setSaving] = useState(false)
    const [crewResponsible, setCrewResponsible] = useState<any>(
        vesselDailyCheck?.crewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )

    const [checkTime, setCheckTime] = useState(
        vesselDailyCheck?.checkTime
            ? dayjs(vesselDailyCheck?.checkTime)
            : dayjs(),
    )

    const handleSafetyChecks = async (check: Boolean, value: any) => {
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck.id,
                logBookEntryID: logentryID,
                [value]: check ? 'Ok' : 'Not Ok',
            }
            if (offline) {
                const newVesselDailyCheck =
                    await dailyCheckModel.save(variables)
                setSaving(true)
                setVesselDailyCheck([newVesselDailyCheck])
                const sections = logbook.logBookEntrySections.nodes
                const section = {
                    className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                    id: `${vesselDailyCheck.id}`,
                    logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                    __typename: 'VesselDailyCheck_LogBookEntrySection',
                }
                if (
                    !sections.some(
                        (s: any) =>
                            JSON.stringify(s) === JSON.stringify(section),
                    )
                ) {
                    sections.push(section)
                }
                const lb = {
                    ...logbook,
                    logBookEntrySections: { nodes: sections },
                }
                await logBookModel.save(lb)
                getOfflineLogBookEntry()
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    // This function is now simplified to just update state
    // The actual saving happens in handleSave
    const handleCheckTime = async (date: any) => {
        setCheckTime(dayjs(date))
    }

    // CrewChecker component handles setting current time

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing safety check', error)
            },
        },
    )

    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    // Get the safety check fields - define this before any useEffect that depends on it
    const fields = getSafetyCheckFields(logBookConfig, vesselDailyCheck)

    // Define the save function that will be exposed to the parent component
    const handleSave = async () => {
        if (offline) {
            const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
            setVesselDailyCheck(data)
            setSaving(true)
        } else {
            getSectionVesselDailyCheck_LogBookEntrySection({
                variables: {
                    id: [vesselDailyCheck.id],
                },
            })
        }

        // Handle section comment
        let id = 0
        if (offline) {
            const comments = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            const safetySectionComment = comments.filter(
                (comment: any) =>
                    comment.fieldName === 'Safety' &&
                    comment.commentType === 'Section',
            )
            if (safetySectionComment.length > 0) {
                id = safetySectionComment[0].id
            } else {
                id = generateUniqueId()
            }
        }
        const variables = {
            id: id,
            fieldName: 'Safety',
            comment: sectionComment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        if (id > 0 && offline) {
            await commentModel.save(variables)
            loadSectionMemberComments()
        } else if (!offline && id > 0) {
            updateSectionMemberComment({
                variables: { input: variables },
            })
        } else if (sectionComment != '') {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                // createSectionMemberComment({
                //     variables: { input: variables },
                // })
            }
        }
        const crewResponsibleIDs = crewResponsible?.map(
            (member: any) => member.value,
        )

        if (vesselDailyCheck?.id) {
            if (offline) {
                const crews = crewResponsibleIDs
                    ? await memberModel.getByIds(crewResponsibleIDs)
                    : []
                const variables = {
                    id: vesselDailyCheck?.id,
                    logBookEntryID: logentryID,
                    crewResponsible: { nodes: crews },
                    checkTime: checkTime
                        ? dayjs(checkTime).format('YYYY-MM-DD HH:mm')
                        : dayjs().format('YYYY-MM-DD HH:mm'),
                }
                const newVesselDailyCheck =
                    await dailyCheckModel.save(variables)
                setSaving(true)
                setVesselDailyCheck([newVesselDailyCheck])
                const sections = logbook.logBookEntrySections.nodes
                const section = {
                    className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                    id: `${vesselDailyCheck.id}`,
                    logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                    __typename: 'VesselDailyCheck_LogBookEntrySection',
                }
                if (
                    !sections.some(
                        (s: any) =>
                            JSON.stringify(s) === JSON.stringify(section),
                    )
                ) {
                    sections.push(section)
                }
                const lb = {
                    ...logbook,
                    logBookEntrySections: { nodes: sections },
                }
                await logBookModel.save(lb)
                getOfflineLogBookEntry()
            } else {
                const variables = {
                    id: vesselDailyCheck?.id,
                    logBookEntryID: logentryID,
                    crewResponsible: crewResponsibleIDs?.join(',') || '',
                    checkTime: checkTime
                        ? dayjs(checkTime).format('YYYY-MM-DD HH:mm')
                        : dayjs().format('YYYY-MM-DD HH:mm'),
                }
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const getOfflineLogBookEntry = async () => {
        const logbook = await logBookModel.getById(logentryID)
        if (logbook) {
            handleSetLogbook(logbook)
        }
    }

    const saveSectionComment = () => {
        if (getComment('Safety', 'Section')?.id > 0) {
            updateSectionMemberComment({
                variables: {
                    input: {
                        id: getComment('Safety', 'Section')?.id,
                        comment: sectionComment,
                    },
                },
            })
        } else {
            createSectionMemberComment({
                variables: {
                    input: {
                        fieldName: 'Safety',
                        comment: sectionComment,
                        logBookEntryID: +logentryID,
                        logBookEntrySectionID: vesselDailyCheck.id,
                        commentType: 'Section',
                    },
                },
            })
        }
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    return (
        <Card className="space-y-6">
            {logBookConfig && vesselDailyCheck && (
                <CheckField>
                    {(getFilteredFields(fields, false, logBookConfig)
                        .sort(sortCustomisedComponentFields)
                        .filter((field: any) =>
                            displayField(field.name, logBookConfig),
                        ).length > 0 ||
                        getFilteredFields(fields, true, logBookConfig)?.filter(
                            (groupField: any) =>
                                displayField(groupField.name, logBookConfig),
                        )?.length > 0) && <CheckFieldTopContent />}
                    <CheckFieldContent>
                        {getFilteredFields(fields, false, logBookConfig)
                            .sort(sortCustomisedComponentFields)
                            .map((field: any, index: number) => (
                                <DailyCheckField
                                    offline={offline}
                                    locked={
                                        !edit_logBookEntry
                                            ? !edit_logBookEntry
                                            : locked
                                    }
                                    key={index}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handleSafetyChecks(false, field.value)
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleSafetyChecks(true, field.value)
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    onCommentSave={(commentText) => {
                                        const comment = getComment(field.name)
                                        let id = comment?.id ? comment.id : 0
                                        if (offline) {
                                            id = comment?.id
                                                ? comment.id
                                                : generateUniqueId()
                                        }
                                        const variables = {
                                            id: id,
                                            fieldName: field.name,
                                            comment: commentText,
                                            logBookEntryID: +logentryID,
                                            logBookEntrySectionID:
                                                vesselDailyCheck.id,
                                            commentType: 'FieldComment',
                                        }
                                        if (comment) {
                                            if (offline) {
                                                commentModel.save(variables)
                                                loadSectionMemberComments()
                                            } else {
                                                updateSectionMemberComment({
                                                    variables: {
                                                        input: variables,
                                                    },
                                                })
                                            }
                                        } else {
                                            if (offline) {
                                                commentModel.save(variables)
                                                loadSectionMemberComments()
                                            } else {
                                                createSectionMemberComment({
                                                    variables: {
                                                        input: variables,
                                                    },
                                                })
                                            }
                                        }
                                    }}
                                    comment={getComment(field.name)?.comment}
                                    displayImage={true}
                                    fieldImages={fieldImages}
                                    onImageUpload={refreshImages}
                                />
                            ))}
                        {getFilteredFields(fields, true, logBookConfig)
                            ?.filter((groupField: any) =>
                                displayField(groupField.name, logBookConfig),
                            )
                            ?.map((groupField: any) => (
                                <div
                                    key={groupField.name}
                                    className="flex flex-wrap sm:flex-row gap-2 text-left items-center justify-between">
                                    <div className="flex flex-col flex-grow">
                                        {groupField?.items
                                            ?.filter((field: any) =>
                                                displayField(
                                                    field.name,
                                                    logBookConfig,
                                                ),
                                            )
                                            ?.map(
                                                (field: any, index: number) => (
                                                    <DailyCheckField
                                                        locked={
                                                            !edit_logBookEntry
                                                                ? !edit_logBookEntry
                                                                : locked
                                                        }
                                                        key={index}
                                                        displayField={displayField(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        displayDescription={displayDescription(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        displayLabel={getFieldLabel(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        inputId={field.value}
                                                        handleNoChange={() =>
                                                            handleSafetyChecks(
                                                                false,
                                                                field.value,
                                                            )
                                                        }
                                                        defaultNoChecked={
                                                            field.checked ===
                                                            'Not_Ok'
                                                        }
                                                        handleYesChange={() =>
                                                            handleSafetyChecks(
                                                                true,
                                                                field.value,
                                                            )
                                                        }
                                                        defaultYesChecked={
                                                            field.checked ===
                                                            'Ok'
                                                        }
                                                        onCommentSave={(
                                                            commentText,
                                                        ) => {
                                                            const comment =
                                                                getComment(
                                                                    field.name,
                                                                )
                                                            let id = comment?.id
                                                                ? comment.id
                                                                : 0
                                                            if (offline) {
                                                                id = comment?.id
                                                                    ? comment.id
                                                                    : generateUniqueId()
                                                            }
                                                            const variables = {
                                                                id: id,
                                                                fieldName:
                                                                    field.name,
                                                                comment:
                                                                    commentText,
                                                                logBookEntryID:
                                                                    +logentryID,
                                                                logBookEntrySectionID:
                                                                    vesselDailyCheck.id,
                                                                commentType:
                                                                    'FieldComment',
                                                            }
                                                            if (comment) {
                                                                if (offline) {
                                                                    commentModel.save(
                                                                        variables,
                                                                    )
                                                                    loadSectionMemberComments()
                                                                } else {
                                                                    updateSectionMemberComment(
                                                                        {
                                                                            variables:
                                                                                {
                                                                                    input: variables,
                                                                                },
                                                                        },
                                                                    )
                                                                }
                                                            } else {
                                                                if (offline) {
                                                                    commentModel.save(
                                                                        variables,
                                                                    )
                                                                    loadSectionMemberComments()
                                                                } else {
                                                                    createSectionMemberComment(
                                                                        {
                                                                            variables:
                                                                                {
                                                                                    input: variables,
                                                                                },
                                                                        },
                                                                    )
                                                                }
                                                            }
                                                        }}
                                                        comment={
                                                            getComment(
                                                                field.name,
                                                            )?.comment
                                                        }
                                                        displayImage={true}
                                                        fieldImages={
                                                            fieldImages
                                                        }
                                                        onImageUpload={
                                                            refreshImages
                                                        }
                                                    />
                                                ),
                                            )}
                                    </div>
                                </div>
                            ))}
                    </CheckFieldContent>
                </CheckField>
            )}

            <CrewChecker
                vesselDailyCheckID={vesselDailyCheck.id}
                crewKey="CrewResponsible"
                timeKey="CheckTime"
                logBookConfig={logBookConfig}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                setCrewResponsible={setCrewResponsible}
                crewResponsible={crewResponsible}
                checkTime={checkTime}
                handleCheckTime={handleCheckTime}
                setCheckTime={setCheckTime}
                offline={offline}
            />

            <Label label="Comments">
                <Textarea
                    id={`section_comment`}
                    rows={4}
                    disabled={locked || !edit_logBookEntry}
                    placeholder="Comments on safety checks ..."
                    onChange={(e) => {
                        setSectionComment(e.target.value)
                        debounceSaveSectionComment()
                    }}
                    defaultValue={getComment('Safety', 'Section')?.comment}
                    className="resize-none"
                    // onBlur={(e) => saveSectionComment()}
                />
            </Label>
        </Card>
    )
}
