'use client'

import React, { Di<PERSON><PERSON>, useMemo, useState } from 'react'
import { useEngineFields } from '../use-engine-fields'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import <PERSON><PERSON><PERSON><PERSON> from '../../crew-checker'
import { useMutation } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import { AlertCircle } from 'lucide-react'
import dayjs from 'dayjs'
import { Button } from '@/components/ui'
import {
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { useSearchParams } from 'next/navigation'
import { useLazyQuery } from '@apollo/client'
import { GET_SECTION_MEMBER_IMAGES } from '@/app/lib/graphQL/query'
interface IProps {
    logentryID: any
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    offline?: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    fieldImages: any
    refreshImages: any
}

export default function PreFields({
    logentryID,
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    offline = false,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    fieldImages,
    refreshImages,
}: IProps) {
    const { preFields } = useEngineFields(logBookConfig, vesselDailyCheck)

    const [preCrewResponsible, setPreCrewResponsible] = useState<any>(
        vesselDailyCheck?.preCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing engine check', error)
            },
        },
    )

    const handlePreCrewResponsible = async (crews: any) => {
        setPreCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                preCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log(' offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const shouldDisplay = useMemo(() => {
        return (
            getFilteredFields(preFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ).length > 0
        )
    }, [logBookConfig])

    const [preCheckTime, setPreCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.preCheckTime ?? new Date()),
    )

    const handlePreCheckTime = async (date: any) => {
        setPreCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                preCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log(' offline')
                /* const newVesselDailyCheck =
                              await dailyCheckModel.save(variables)
                          // setSaving(true)
                          setSaving(false)
                          setVesselDailyCheck([newVesselDailyCheck])
                          const sections = logbook.logBookEntrySections.nodes
                          const section = {
                              className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                              id: `${vesselDailyCheck.id}`,
                              logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                              __typename: 'VesselDailyCheck_LogBookEntrySection',
                          }
                          if (
                              !sections.some(
                                  (s: any) =>
                                      JSON.stringify(s) === JSON.stringify(section),
                              )
                          ) {
                              sections.push(section)
                          }
                          const lb = {
                              ...logbook,
                              logBookEntrySections: { nodes: sections },
                          }
                          await logBookModel.save(lb)
                          getOfflineLogBookEntry() */
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const filteredFields = useMemo(() => {
        return (
            getFilteredFields(preFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [preFields, logBookConfig])

    if (!shouldDisplay) {
        return <></>
    }



    return (
        <>
            {logBookConfig && vesselDailyCheck && (
                <React.Fragment>
                    {filteredFields.filter((groupField: any) =>
                        groupField?.items?.some((field: any) =>
                            displayField(field.name, logBookConfig),
                        ),
                    ).length > 0 && <CheckFieldTopContent />}
                    {filteredFields.map((groupField: any) => (
                        <React.Fragment key={groupField.name}>
                            <React.Fragment>
                                {groupField?.items
                                    ?.filter((field: any) =>
                                        displayField(field.name, logBookConfig),
                                    )
                                    ?.map((field: any, index: number) => (
                                        <React.Fragment key={`field-${index}`}>
                                            <DailyCheckField
                                                locked={
                                                    locked || !edit_logBookEntry
                                                }
                                                key={index}
                                                displayField={displayField(
                                                    field.name,
                                                    logBookConfig,
                                                )}
                                                displayDescription={displayDescription(
                                                    field.name,
                                                    logBookConfig,
                                                )}
                                                setOpenDescriptionPanel={
                                                    setOpenDescriptionPanel
                                                }
                                                setDescriptionPanelHeading={
                                                    setDescriptionPanelHeading
                                                }
                                                displayLabel={getFieldLabel(
                                                    field.name,
                                                    logBookConfig,
                                                )}
                                                inputId={field.value}
                                                handleNoChange={() =>
                                                    // field.handleChange(
                                                    //     false,
                                                    // )
                                                    handleEngineChecks(
                                                        false,
                                                        field.value,
                                                    )
                                                }
                                                defaultNoChecked={
                                                    field.checked === 'Not_Ok'
                                                }
                                                handleYesChange={() =>
                                                    // field.handleChange(
                                                    //     true,
                                                    // )
                                                    handleEngineChecks(
                                                        true,
                                                        field.value,
                                                    )
                                                }
                                                defaultYesChecked={
                                                    field.checked === 'Ok'
                                                }
                                                commentAction={() =>
                                                    showCommentPopup(
                                                        getComment(field.name),
                                                        composeField(
                                                            field.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                }
                                                comment={
                                                    getComment(field.name)
                                                        ?.comment
                                                }
                                                displayImage={true}
                                                fieldImages={fieldImages}
                                                onImageUpload={refreshImages}
                                            />
                                        </React.Fragment>
                                    ))}
                                {displayDescription(
                                    groupField.name,
                                    logBookConfig,
                                ) && (
                                    <Button
                                        variant="text"
                                        iconLeft={AlertCircle}
                                        onClick={() => {
                                            setDescriptionPanelContent(
                                                displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ),
                                            )
                                            setOpenDescriptionPanel(true)
                                            setDescriptionPanelHeading(
                                                groupField.name,
                                            )
                                        }}
                                    />
                                )}
                            </React.Fragment>
                        </React.Fragment>
                    ))}
                </React.Fragment>
            )}

            <div className="mt-6">
                <CrewChecker
                    crewKey="PreCrewResponsible"
                    timeKey="PreCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handlePreCrewResponsible}
                    crewResponsible={preCrewResponsible}
                    checkTime={preCheckTime}
                    handleCheckTime={handlePreCheckTime}
                    setCheckTime={setPreCheckTime}
                />
            </div>
        </>
    )
}
